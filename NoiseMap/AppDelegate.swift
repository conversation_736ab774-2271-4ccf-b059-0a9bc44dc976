//
//  AppDelegate.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        window = UIWindow(frame: UIScreen.main.bounds)

        // 创建一个简单的测试控制器
        let testViewController = createTestViewController()
        let navigationController = UINavigationController(rootViewController: testViewController)

        window?.rootViewController = navigationController
        window?.makeKeyAndVisible()

        return true
    }

    private func createTestViewController() -> UIViewController {
        let viewController = UIViewController()
        viewController.view.backgroundColor = UIColor.systemBackground
        viewController.title = "Perception Test Lab"

        let scrollView = UIScrollView()
        let contentView = UIView()
        let stackView = UIStackView()

        viewController.view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(stackView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(viewController.view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.alignment = .fill

        // Add title
        let titleLabel = UILabel()
        titleLabel.text = "🌐 Simulation Control Panel"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textAlignment = .center
        stackView.addArrangedSubview(titleLabel)

        // Add scenario selection area
        let scenarioCard = createCard(title: "Scenario Presets")
        let scenarios = [
            ("⛆", "Rainy Night Return", "Heavy rain on highway"),
            ("🌬", "Highway Wind Shear", "High-speed crosswinds"),
            ("🛻", "Heavy Traffic Bridge", "Truck rumble overhead"),
            ("🅿️", "Parking Garage Rumble", "Low-frequency resonance"),
            ("🕓", "Morning Idle", "Quiet garage startup")
        ]

        for (icon, title, subtitle) in scenarios {
            let button = createScenarioButton(icon: icon, title: title, subtitle: subtitle)
            scenarioCard.addArrangedSubview(button)
        }
        stackView.addArrangedSubview(scenarioCard)

        // Add custom configuration area
        let configCard = createCard(title: "Custom Configuration")
        configCard.addArrangedSubview(createSlider(title: "🌀 Driving Speed", value: 60, unit: "km/h"))
        configCard.addArrangedSubview(createSlider(title: "🛞 Tire Noise Ratio", value: 50, unit: "%"))
        configCard.addArrangedSubview(createSlider(title: "💨 Wind Noise Ratio", value: 50, unit: "%"))
        configCard.addArrangedSubview(createSlider(title: "🏗 Structural Transmission", value: 50, unit: "%"))
        stackView.addArrangedSubview(configCard)

        // Add start button
        let startButton = UIButton(type: .system)
        startButton.setTitle("▶️ Start Simulation", for: .normal)
        startButton.backgroundColor = UIColor.systemBlue
        startButton.setTitleColor(.white, for: .normal)
        startButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        startButton.layer.cornerRadius = 12
        startButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
        stackView.addArrangedSubview(startButton)

        return viewController
    }

    private func createCard(title: String) -> UIStackView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor.systemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill

        containerView.addSubview(titleLabel)
        containerView.addSubview(stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }

        let wrapperStackView = UIStackView()
        wrapperStackView.axis = .vertical
        wrapperStackView.addArrangedSubview(containerView)

        return stackView
    }

    private func createScenarioButton(icon: String, title: String, subtitle: String) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.systemGray6
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 4
        stackView.alignment = .center
        stackView.isUserInteractionEnabled = false

        let iconLabel = UILabel()
        iconLabel.text = icon
        iconLabel.font = UIFont.systemFont(ofSize: 24)

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor.secondaryLabel

        stackView.addArrangedSubview(iconLabel)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)

        button.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.edges.equalToSuperview().inset(12)
        }

        button.snp.makeConstraints { make in
            make.height.equalTo(80)
        }

        return button
    }

    private func createSlider(title: String, value: Float, unit: String) -> UIView {
        let containerView = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let valueLabel = UILabel()
        valueLabel.text = "\(Int(value))\(unit)"
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        valueLabel.textColor = UIColor.systemBlue

        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = value == 60 ? 150 : 100
        slider.value = value
        slider.tintColor = UIColor.systemBlue

        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        containerView.addSubview(slider)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(30)
        }

        return containerView
    }

}

