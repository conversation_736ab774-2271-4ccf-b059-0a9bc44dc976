//
//  NoiseSimulationPlayback.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

protocol NoiseSimulationPlaybackDelegate: AnyObject {
    func simulationDidStart()
    func simulationDidComplete()
    func simulationDidPause()
}

class NoiseSimulationPlayback: UIView {

    // MARK: - Properties
    weak var delegate: NoiseSimulationPlaybackDelegate?
    private var isPlaying = false

    // MARK: - UI Components
    private let mainStackView = UIStackView()

    // Animation Area
    private let animationCard = UIView()
    private let animationView = UIView()

    // Controls
    private let controlsCard = UIView()
    private let playPauseButton = UIButton(type: .system)
    private let stopButton = UIButton(type: .system)
    private let progressView = UIProgressView(progressViewStyle: .default)
    private let timeLabel = UILabel()

    // Audio Settings
    private let audioCard = UIView()
    private let volumeSlider = UISlider()
    private let audioModeSegmentedControl = UISegmentedControl(items: ["Headphones", "Car Speakers"])

    // Status
    private let statusLabel = UILabel()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = UIColor.systemBackground
        setupMainStackView()
        setupAnimationSection()
        setupControlsSection()
        setupAudioSection()
        setupLayout()
        setupActions()
        updateUI()
    }

    private func setupMainStackView() {
        mainStackView.axis = .vertical
        mainStackView.spacing = 20
        mainStackView.alignment = .fill
    }

    private func setupAnimationSection() {
        // Setup animation card
        animationCard.backgroundColor = UIColor.systemBackground
        animationCard.layer.cornerRadius = 12
        animationCard.layer.shadowColor = UIColor.black.cgColor
        animationCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        animationCard.layer.shadowRadius = 4
        animationCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "🔊 Noise Visualization"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        // Animation view
        animationView.backgroundColor = UIColor.systemGray6
        animationView.layer.cornerRadius = 8

        let placeholderLabel = UILabel()
        placeholderLabel.text = "🚗 Car Interior Simulation\n🌊 Sound Wave Visualization"
        placeholderLabel.font = UIFont.systemFont(ofSize: 16)
        placeholderLabel.textColor = UIColor.secondaryLabel
        placeholderLabel.textAlignment = .center
        placeholderLabel.numberOfLines = 0

        animationView.addSubview(placeholderLabel)
        placeholderLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }

        animationCard.addSubview(titleLabel)
        animationCard.addSubview(animationView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        animationView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(200)
        }
    }

    private func setupControlsSection() {
        // Setup controls card
        controlsCard.backgroundColor = UIColor.systemBackground
        controlsCard.layer.cornerRadius = 12
        controlsCard.layer.shadowColor = UIColor.black.cgColor
        controlsCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        controlsCard.layer.shadowRadius = 4
        controlsCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "🎮 Playback Controls"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        // Setup buttons
        playPauseButton.setTitle("▶️ Play", for: .normal)
        playPauseButton.backgroundColor = UIColor.systemBlue
        playPauseButton.setTitleColor(.white, for: .normal)
        playPauseButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        playPauseButton.layer.cornerRadius = 8

        stopButton.setTitle("⏹ Stop", for: .normal)
        stopButton.backgroundColor = UIColor.systemGray
        stopButton.setTitleColor(.white, for: .normal)
        stopButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        stopButton.layer.cornerRadius = 8

        // Button container
        let buttonStackView = UIStackView()
        buttonStackView.axis = .horizontal
        buttonStackView.spacing = 12
        buttonStackView.distribution = .fillEqually

        buttonStackView.addArrangedSubview(playPauseButton)
        buttonStackView.addArrangedSubview(stopButton)

        // Progress container
        progressView.progressTintColor = UIColor.systemBlue
        progressView.trackTintColor = UIColor.systemGray4

        timeLabel.text = "00:00 / 00:00"
        timeLabel.font = UIFont.systemFont(ofSize: 14)
        timeLabel.textColor = UIColor.secondaryLabel

        let progressStackView = UIStackView()
        progressStackView.axis = .horizontal
        progressStackView.spacing = 8
        progressStackView.alignment = .center

        progressStackView.addArrangedSubview(progressView)
        progressStackView.addArrangedSubview(timeLabel)

        let controlsStackView = UIStackView()
        controlsStackView.axis = .vertical
        controlsStackView.spacing = 16
        controlsStackView.alignment = .fill

        controlsStackView.addArrangedSubview(buttonStackView)
        controlsStackView.addArrangedSubview(progressStackView)

        controlsCard.addSubview(titleLabel)
        controlsCard.addSubview(controlsStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        controlsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }

        playPauseButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }

        stopButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }

        timeLabel.snp.makeConstraints { make in
            make.width.equalTo(80)
        }
    }

    private func setupAudioSection() {
        // Setup audio card
        audioCard.backgroundColor = UIColor.systemBackground
        audioCard.layer.cornerRadius = 12
        audioCard.layer.shadowColor = UIColor.black.cgColor
        audioCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        audioCard.layer.shadowRadius = 4
        audioCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "🎧 Audio Settings"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        // Volume control
        let volumeContainer = createSliderContainer(title: "🔊 Volume", slider: volumeSlider, minValue: 0, maxValue: 100, unit: "%")

        // Audio mode selection
        audioModeSegmentedControl.selectedSegmentIndex = 0
        audioModeSegmentedControl.selectedSegmentTintColor = UIColor.systemBlue
        audioModeSegmentedControl.backgroundColor = UIColor.systemGray6

        let audioModeContainer = createSegmentedControlContainer(
            title: "🎧 Audio Output",
            control: audioModeSegmentedControl
        )

        let audioStackView = UIStackView()
        audioStackView.axis = .vertical
        audioStackView.spacing = 16
        audioStackView.alignment = .fill

        audioStackView.addArrangedSubview(volumeContainer)
        audioStackView.addArrangedSubview(audioModeContainer)

        audioCard.addSubview(titleLabel)
        audioCard.addSubview(audioStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        audioStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }

    private func setupLayout() {
        addSubview(mainStackView)

        mainStackView.addArrangedSubview(animationCard)
        mainStackView.addArrangedSubview(controlsCard)
        mainStackView.addArrangedSubview(audioCard)
        mainStackView.addArrangedSubview(statusLabel)

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
    }

    private func setupActions() {
        playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)
        stopButton.addTarget(self, action: #selector(stopButtonTapped), for: .touchUpInside)
        volumeSlider.addTarget(self, action: #selector(volumeChanged(_:)), for: .valueChanged)
        audioModeSegmentedControl.addTarget(self, action: #selector(audioModeChanged), for: .valueChanged)
    }

    private func updateUI() {
        statusLabel.text = isPlaying ? "🔊 Playing simulation..." : "⏸ Ready to play"
        statusLabel.font = UIFont.systemFont(ofSize: 16)
        statusLabel.textColor = isPlaying ? UIColor.systemBlue : UIColor.secondaryLabel
        statusLabel.textAlignment = .center

        playPauseButton.setTitle(isPlaying ? "⏸ Pause" : "▶️ Play", for: .normal)
        playPauseButton.backgroundColor = isPlaying ? UIColor.systemOrange : UIColor.systemBlue
        stopButton.isEnabled = isPlaying
        stopButton.backgroundColor = stopButton.isEnabled ? UIColor.systemRed : UIColor.systemGray
    }

    // MARK: - Helper Methods
    private func createSliderContainer(title: String, slider: UISlider, minValue: Float, maxValue: Float, unit: String) -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let valueLabel = UILabel()
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        valueLabel.textColor = UIColor.systemBlue

        slider.minimumValue = minValue
        slider.maximumValue = maxValue
        slider.value = 50 // Default value
        slider.tintColor = UIColor.systemBlue

        // Store valueLabel and unit as associated objects
        objc_setAssociatedObject(slider, "valueLabel", valueLabel, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        objc_setAssociatedObject(slider, "unit", unit, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        // Update initial value
        valueLabel.text = "\(Int(slider.value))\(unit)"

        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        container.addSubview(slider)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(30)
        }

        return container
    }

    private func createSegmentedControlContainer(title: String, control: UISegmentedControl) -> UIView {
        let container = UIView()
        let titleLabel = UILabel()

        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        control.setTitleTextAttributes([.foregroundColor: UIColor.label], for: .normal)

        container.addSubview(titleLabel)
        container.addSubview(control)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        control.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(32)
        }

        return container
    }

    // MARK: - Actions
    @objc private func playPauseButtonTapped() {
        if isPlaying {
            pauseSimulation()
        } else {
            startSimulation()
        }
    }

    @objc private func stopButtonTapped() {
        stopSimulation()
    }

    @objc private func volumeChanged(_ sender: UISlider) {
        if let valueLabel = objc_getAssociatedObject(sender, "valueLabel") as? UILabel,
           let unit = objc_getAssociatedObject(sender, "unit") as? String {
            valueLabel.text = "\(Int(sender.value))\(unit)"
        }
    }

    @objc private func audioModeChanged() {
        let mode = audioModeSegmentedControl.selectedSegmentIndex == 0 ? "Headphones" : "Car Speakers"
        print("Audio mode changed to: \(mode)")
    }

    // MARK: - Public Methods
    func startSimulation() {
        guard !isPlaying else { return }
        isPlaying = true
        progressView.progress = 0.0
        updateUI()
        delegate?.simulationDidStart()
        statusLabel.text = "🔊 Simulation started"
    }

    func pauseSimulation() {
        guard isPlaying else { return }
        isPlaying = false
        updateUI()
        delegate?.simulationDidPause()
        statusLabel.text = "⏸ Simulation paused"
    }

    func stopSimulation() {
        isPlaying = false
        progressView.progress = 0.0
        timeLabel.text = "00:00 / 00:00"
        updateUI()
        delegate?.simulationDidComplete()
        statusLabel.text = "⏹ Simulation stopped"
    }
}
