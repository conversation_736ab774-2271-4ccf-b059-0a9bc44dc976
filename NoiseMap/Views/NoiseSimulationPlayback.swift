//
//  NoiseSimulationPlayback.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit
import AVFoundation

protocol NoiseSimulationPlaybackDelegate: AnyObject {
    func simulationDidStart()
    func simulationDidComplete()
    func simulationDidPause()
}

class NoiseSimulationPlayback: UIView {

    // MARK: - Properties
    weak var delegate: NoiseSimulationPlaybackDelegate?

    private var currentSettings: SimulationSettings?
    private var isPlaying = false
    private var animationTimer: Timer?
    private var audioPlayer: AVAudioPlayer?

    // MARK: - UI Components
    private let mainStackView = UIStackView()

    // Animation Area
    private let animationCard = CardView(title: "🔊 Noise Visualization")
    private let carInteriorView = CarInteriorView()
    private let soundWaveView = SoundWaveView()

    // Controls
    private let controlsCard = CardView(title: "🎮 Playback Controls")
    private let playPauseButton = ThemedButton(style: .accent, title: "▶️ Play")
    private let stopButton = ThemedButton(style: .secondary, title: "⏹ Stop")
    private let progressView = UIProgressView(progressViewStyle: .default)
    private let timeLabel = UILabel()

    // Audio Settings
    private let audioCard = CardView(title: "🎧 Audio Settings")
    private let volumeSlider = ThemedSlider(title: "🔊 Volume", minValue: 0, maxValue: 100, unit: "%")
    private let audioModeSegmentedControl = UISegmentedControl(items: ["Headphones", "Car Speakers"])
    private let frequencyFilterSegmentedControl = UISegmentedControl(items: ["Full Range", "Low Pass", "High Pass"])

    // Status
    private let statusLabel = UILabel()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = AppTheme.Colors.background
        setupComponents()
        setupLayout()
        setupActions()
        updateUI()
    }

    private func setupComponents() {
        mainStackView.axis = .vertical
        mainStackView.spacing = AppTheme.Spacing.lg
        mainStackView.alignment = .fill

        // Animation setup
        setupAnimationArea()

        // Controls setup
        setupControls()

        // Audio settings setup
        setupAudioSettings()

        // Status setup
        setupStatus()
    }

    private func setupAnimationArea() {
        let animationContainer = UIView()

        carInteriorView.backgroundColor = AppTheme.Colors.darkBackground
        carInteriorView.layer.cornerRadius = AppTheme.CornerRadius.medium

        soundWaveView.backgroundColor = UIColor.clear

        animationContainer.addSubview(carInteriorView)
        animationContainer.addSubview(soundWaveView)

        carInteriorView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(200)
        }

        soundWaveView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        animationCard.addContent(animationContainer)
    }

    private func setupControls() {
        let controlsContainer = UIView()
        let buttonStackView = UIStackView()

        buttonStackView.axis = .horizontal
        buttonStackView.spacing = AppTheme.Spacing.md
        buttonStackView.distribution = .fillEqually

        buttonStackView.addArrangedSubview(playPauseButton)
        buttonStackView.addArrangedSubview(stopButton)

        progressView.progressTintColor = AppTheme.Colors.accent
        progressView.trackTintColor = AppTheme.Colors.separator

        timeLabel.font = AppTheme.Typography.caption
        timeLabel.textColor = AppTheme.Colors.textSecondary
        timeLabel.textAlignment = .center
        timeLabel.text = "00:00 / 00:00"

        controlsContainer.addSubview(buttonStackView)
        controlsContainer.addSubview(progressView)
        controlsContainer.addSubview(timeLabel)

        buttonStackView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }

        progressView.snp.makeConstraints { make in
            make.top.equalTo(buttonStackView.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(4)
        }

        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(progressView.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
        }

        controlsCard.addContent(controlsContainer)
    }

    private func setupAudioSettings() {
        let audioContainer = UIView()
        let audioStackView = UIStackView()

        audioStackView.axis = .vertical
        audioStackView.spacing = AppTheme.Spacing.md
        audioStackView.alignment = .fill

        // Audio mode container
        let audioModeContainer = createSegmentedControlContainer(
            title: "🎧 Audio Output Mode",
            control: audioModeSegmentedControl
        )

        // Frequency filter container
        let filterContainer = createSegmentedControlContainer(
            title: "🎛 Frequency Filter",
            control: frequencyFilterSegmentedControl
        )

        audioStackView.addArrangedSubview(volumeSlider)
        audioStackView.addArrangedSubview(audioModeContainer)
        audioStackView.addArrangedSubview(filterContainer)

        audioContainer.addSubview(audioStackView)
        audioStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        audioCard.addContent(audioContainer)
    }

    private func setupStatus() {
        statusLabel.font = AppTheme.Typography.callout
        statusLabel.textColor = AppTheme.Colors.textSecondary
        statusLabel.textAlignment = .center
        statusLabel.text = "Ready to simulate"
        statusLabel.numberOfLines = 0
    }

    private func setupLayout() {
        addSubview(mainStackView)

        mainStackView.addArrangedSubview(animationCard)
        mainStackView.addArrangedSubview(controlsCard)
        mainStackView.addArrangedSubview(audioCard)
        mainStackView.addArrangedSubview(statusLabel)

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupActions() {
        playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)
        stopButton.addTarget(self, action: #selector(stopButtonTapped), for: .touchUpInside)

        volumeSlider.onValueChanged = { [weak self] value in
            self?.updateVolume(value / 100.0)
        }

        audioModeSegmentedControl.addTarget(self, action: #selector(audioModeChanged), for: .valueChanged)
        frequencyFilterSegmentedControl.addTarget(self, action: #selector(frequencyFilterChanged), for: .valueChanged)
    }

    private func updateUI() {
        volumeSlider.value = 70 // Default volume
        audioModeSegmentedControl.selectedSegmentIndex = 0
        frequencyFilterSegmentedControl.selectedSegmentIndex = 0
        progressView.progress = 0
    }

    // MARK: - Helper Methods
    private func createSegmentedControlContainer(title: String, control: UISegmentedControl) -> UIView {
        let container = UIView()
        let titleLabel = UILabel()

        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.callout
        titleLabel.textColor = AppTheme.Colors.textPrimary

        control.selectedSegmentTintColor = AppTheme.Colors.accent
        control.backgroundColor = AppTheme.Colors.background
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textLight], for: .selected)
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textPrimary], for: .normal)

        container.addSubview(titleLabel)
        container.addSubview(control)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        control.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(32)
        }

        return container
    }

    // MARK: - Animation Methods
    private func startAnimation() {
        guard let settings = currentSettings else { return }

        carInteriorView.startVisualization(with: settings)
        soundWaveView.startWaveAnimation(with: settings)

        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateProgress()
        }
    }

    private func stopAnimation() {
        carInteriorView.stopVisualization()
        soundWaveView.stopWaveAnimation()
        animationTimer?.invalidate()
        animationTimer = nil
    }

    private func updateProgress() {
        // Simulate progress for demo purposes
        let currentProgress = progressView.progress + 0.01
        if currentProgress >= 1.0 {
            stopSimulation()
        } else {
            progressView.progress = currentProgress
            let elapsed = Int(currentProgress * 30) // 30 second simulation
            timeLabel.text = String(format: "%02d:%02d / 00:30", elapsed / 60, elapsed % 60)
        }
    }

    // MARK: - Audio Methods
    private func updateVolume(_ volume: Float) {
        audioPlayer?.volume = volume
    }

    // MARK: - Actions
    @objc private func playPauseButtonTapped() {
        if isPlaying {
            pauseSimulation()
        } else {
            startSimulation()
        }
    }

    @objc private func stopButtonTapped() {
        stopSimulation()
    }

    @objc private func audioModeChanged() {
        let mode = audioModeSegmentedControl.selectedSegmentIndex == 0 ? "Headphones" : "Car Speakers"
        statusLabel.text = "Audio mode: \(mode)"
    }

    @objc private func frequencyFilterChanged() {
        let filters = ["Full Range", "Low Pass", "High Pass"]
        let filter = filters[frequencyFilterSegmentedControl.selectedSegmentIndex]
        statusLabel.text = "Frequency filter: \(filter)"
    }

    // MARK: - Public Methods
    func configureSimulation(with settings: SimulationSettings) {
        currentSettings = settings
        statusLabel.text = "Configured for \(Int(settings.speed)) km/h simulation"
    }

    func startSimulation() {
        guard currentSettings != nil else {
            statusLabel.text = "Please configure simulation settings first"
            return
        }

        isPlaying = true
        playPauseButton.setTitle("⏸ Pause", for: .normal)
        statusLabel.text = "Simulation running..."

        startAnimation()
        delegate?.simulationDidStart()
    }

    func pauseSimulation() {
        isPlaying = false
        playPauseButton.setTitle("▶️ Play", for: .normal)
        statusLabel.text = "Simulation paused"

        stopAnimation()
        delegate?.simulationDidPause()
    }

    func stopSimulation() {
        isPlaying = false
        playPauseButton.setTitle("▶️ Play", for: .normal)
        statusLabel.text = "Simulation completed"
        progressView.progress = 0
        timeLabel.text = "00:00 / 00:00"

        stopAnimation()
        delegate?.simulationDidComplete()
    }
}