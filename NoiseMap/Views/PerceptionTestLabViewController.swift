//
//  PerceptionTestLabViewController.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class PerceptionTestLabViewController: UIViewController {

    // MARK: - Properties
    private var currentSettings: SimulationSettings?
    private var selectedScenario: NoiseScenario?
    private var isSimulationRunning = false

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let mainStackView = UIStackView()

    // Header
    private let headerView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()

    // Main Components
    private let controlPanel = SimulationControlPanel()
    private let playbackView = NoiseSimulationPlayback()
    private let recorderPanel = PerceptionRecorderPanel()

    // Navigation
    private let segmentedControl = UISegmentedControl(items: ["🌐 Configure", "🔊 Simulate", "📋 Record"])

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        setupDelegates()
        setupLayout()
        setupActions()
        updateUI()

        // 强制显示第一个面板
        segmentedControl.selectedSegmentIndex = 0
        controlPanel.isHidden = false
        controlPanel.alpha = 1.0
        playbackView.isHidden = true
        playbackView.alpha = 0.0
        recorderPanel.isHidden = true
        recorderPanel.alpha = 0.0

        print("🔍 viewDidLoad: controlPanel.isHidden = \(controlPanel.isHidden), alpha = \(controlPanel.alpha)")
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    // MARK: - Setup
    private func setupView() {
        view.backgroundColor = UIColor.systemBackground

        // Configure scroll view
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        // Configure main stack view
        mainStackView.axis = .vertical
        mainStackView.spacing = 20
        mainStackView.alignment = .fill

        setupHeader()
        setupSegmentedControl()
    }

    private func setupHeader() {
        headerView.backgroundColor = UIColor.systemBlue
        headerView.layer.cornerRadius = 16
        headerView.layer.shadowColor = UIColor.black.cgColor
        headerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        headerView.layer.shadowRadius = 4
        headerView.layer.shadowOpacity = 0.1

        titleLabel.text = "Perception Test Lab"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.white
        titleLabel.textAlignment = .center

        subtitleLabel.text = "🌐 Noise Simulation & Perception Analysis"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.white.withAlphaComponent(0.9)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0

        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    private func setupSegmentedControl() {
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.selectedSegmentTintColor = UIColor.systemBlue
        segmentedControl.backgroundColor = UIColor.systemGray6
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.label], for: .normal)
        segmentedControl.layer.cornerRadius = 8
        segmentedControl.layer.shadowColor = UIColor.black.cgColor
        segmentedControl.layer.shadowOffset = CGSize(width: 0, height: 1)
        segmentedControl.layer.shadowRadius = 2
        segmentedControl.layer.shadowOpacity = 0.1
    }

    private func setupDelegates() {
        controlPanel.delegate = self
        playbackView.delegate = self
        recorderPanel.delegate = self
    }

    private func setupLayout() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(mainStackView)

        mainStackView.addArrangedSubview(headerView)
        mainStackView.addArrangedSubview(segmentedControl)
        mainStackView.addArrangedSubview(controlPanel)
        mainStackView.addArrangedSubview(playbackView)
        mainStackView.addArrangedSubview(recorderPanel)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        segmentedControl.snp.makeConstraints { make in
            make.height.equalTo(40)
        }
        
        controlPanel.snp.makeConstraints { make in
            make.height.equalTo(1000)
        }
    }

    private func setupActions() {
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
    }

    private func updateUI() {
        updateVisibleSections()
    }

    // MARK: - Helper Methods
    private func updateVisibleSections() {
        let selectedIndex = segmentedControl.selectedSegmentIndex

        print("🔍 updateVisibleSections called with selectedIndex: \(selectedIndex)")

        // 直接设置，不使用动画，确保立即显示
        controlPanel.isHidden = selectedIndex != 0
        playbackView.isHidden = selectedIndex != 1
        recorderPanel.isHidden = selectedIndex != 2

        controlPanel.alpha = selectedIndex == 0 ? 1.0 : 0.0
        playbackView.alpha = selectedIndex == 1 ? 1.0 : 0.0
        recorderPanel.alpha = selectedIndex == 2 ? 1.0 : 0.0

        print("🔍 controlPanel.isHidden: \(controlPanel.isHidden), alpha: \(controlPanel.alpha)")

        // Update subtitle based on current section
        switch selectedIndex {
        case 0:
            subtitleLabel.text = "🌐 Configure Simulation Parameters"
        case 1:
            subtitleLabel.text = "🔊 Experience Noise Simulation"
        case 2:
            subtitleLabel.text = "📋 Record Your Perception"
        default:
            break
        }
    }

    private func showAlert(title: String, message: String, style: UIAlertController.Style = .alert) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: style)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showConfirmationAlert(title: String, message: String, confirmAction: @escaping () -> Void) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Confirm", style: .default) { _ in
            confirmAction()
        })
        present(alert, animated: true)
    }

    // MARK: - Actions
    @objc private func segmentChanged() {
        updateVisibleSections()

        // Auto-scroll to show the active section
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let selectedIndex = self.segmentedControl.selectedSegmentIndex
            var targetView: UIView

            switch selectedIndex {
            case 0:
                targetView = self.controlPanel
            case 1:
                targetView = self.playbackView
            case 2:
                targetView = self.recorderPanel
            default:
                return
            }

            let targetFrame = targetView.convert(targetView.bounds, to: self.scrollView)
            self.scrollView.scrollRectToVisible(targetFrame, animated: true)
        }
    }
}

// MARK: - SimulationControlPanelDelegate
extension PerceptionTestLabViewController: SimulationControlPanelDelegate {

    func didUpdateSettings(_ settings: SimpleSettings) {
        print("Settings updated: \(settings)")
        // Convert SimpleSettings to SimulationSettings if needed
        // For now, just print the settings
    }

    func didSelectScenario(_ index: Int) {
        print("Scenario selected at index: \(index)")

        let scenarios = [
            "Rainy Night Return",
            "Highway Wind Shear",
            "Heavy Traffic Bridge",
            "Parking Garage Rumble",
            "Morning Idle"
        ]

        if index < scenarios.count {
            showAlert(
                title: "Scenario Selected",
                message: "Selected: \(scenarios[index])"
            )
        }
    }

    func didRequestStartSimulation() {
        print("Start simulation requested")

        // Switch to simulation tab
        segmentedControl.selectedSegmentIndex = 1
        updateVisibleSections()

        showAlert(title: "Simulation Started", message: "Switching to simulation view...")
    }
}

// MARK: - NoiseSimulationPlaybackDelegate
extension PerceptionTestLabViewController: NoiseSimulationPlaybackDelegate {

    func simulationDidStart() {
        isSimulationRunning = true
        showAlert(title: "Simulation Started", message: "Experience the noise simulation and prepare to record your perception.")
    }

    func simulationDidComplete() {
        isSimulationRunning = false

        showConfirmationAlert(
            title: "Simulation Complete",
            message: "The simulation has finished. Would you like to record your perception feedback now?"
        ) {
            // Switch to recorder tab
            self.segmentedControl.selectedSegmentIndex = 2
            self.updateVisibleSections()
        }
    }

    func simulationDidPause() {
        // Optional: Handle pause state
    }
}

// MARK: - PerceptionRecorderPanelDelegate
extension PerceptionTestLabViewController: PerceptionRecorderPanelDelegate {

    func didSaveFeedback(_ feedback: PerceptionFeedback) {
        // TODO: Save feedback to persistent storage
        print("Feedback saved: \(feedback)")

        showAlert(
            title: "Feedback Saved",
            message: "Your perception feedback has been recorded successfully. Thank you for your input!"
        )

        // Optionally reset to configuration panel for next test
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.segmentedControl.selectedSegmentIndex = 0
            self.updateVisibleSections()
        }
    }

    func didRequestSaveProfile(_ feedback: PerceptionFeedback) {
        showConfirmationAlert(
            title: "Save Personal Profile",
            message: "This will create a personal perception profile based on your feedback. This profile can be used for future comparisons and analysis."
        ) {
            // TODO: Create and save personal profile
            self.createPersonalProfile(from: feedback)
        }
    }

    private func createPersonalProfile(from feedback: PerceptionFeedback) {
        // TODO: Implement profile creation logic
        let profile = UserProfile(
            id: UUID().uuidString,
            name: "User Profile \(Date())",
            feedbackHistory: [feedback],
            personalCurve: nil,
            createdAt: Date(),
            lastUpdated: Date()
        )

        print("Personal profile created: \(profile)")

        showAlert(
            title: "Profile Created",
            message: "Your personal perception profile has been created and saved. You can use this for future noise analysis and comparisons."
        )
    }
}
