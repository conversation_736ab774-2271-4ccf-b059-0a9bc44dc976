//
//  PerceptionTestLabViewController.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class PerceptionTestLabViewController: UIViewController {

    // MARK: - Properties
    private var currentSettings: SimulationSettings?
    private var selectedScenario: NoiseScenario?
    private var isSimulationRunning = false

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let mainStackView = UIStackView()

    // Header
    private let headerView = UIView()
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()

    // Main Components
    private let controlPanel = SimulationControlPanel()
    private let playbackView = NoiseSimulationPlayback()
    private let recorderPanel = PerceptionRecorderPanel()

    // Navigation
    private let segmentedControl = UISegmentedControl(items: ["🌐 Configure", "🔊 Simulate", "📋 Record"])

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        setupDelegates()
        setupLayout()
        setupActions()
        updateUI()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    // MARK: - Setup
    private func setupView() {
        view.backgroundColor = AppTheme.Colors.background

        // Configure scroll view
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        // Configure main stack view
        mainStackView.axis = .vertical
        mainStackView.spacing = AppTheme.Spacing.lg
        mainStackView.alignment = .fill

        setupHeader()
        setupSegmentedControl()
    }

    private func setupHeader() {
        headerView.backgroundColor = AppTheme.Colors.primary
        headerView.layer.cornerRadius = AppTheme.CornerRadius.large
        headerView.applyShadow(AppTheme.Shadow.medium)

        titleLabel.text = "Perception Test Lab"
        titleLabel.font = AppTheme.Typography.largeTitle
        titleLabel.textColor = AppTheme.Colors.textLight
        titleLabel.textAlignment = .center

        subtitleLabel.text = "🌐 Noise Simulation & Perception Analysis"
        subtitleLabel.font = AppTheme.Typography.title3
        subtitleLabel.textColor = AppTheme.Colors.accentLight
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0

        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.lg)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.lg)
        }
    }

    private func setupSegmentedControl() {
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.selectedSegmentTintColor = AppTheme.Colors.accent
        segmentedControl.backgroundColor = AppTheme.Colors.cardBackground
        segmentedControl.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textLight], for: .selected)
        segmentedControl.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textPrimary], for: .normal)
        segmentedControl.layer.cornerRadius = AppTheme.CornerRadius.small
        segmentedControl.applyShadow(AppTheme.Shadow.light)
    }

    private func setupDelegates() {
        controlPanel.delegate = self
        playbackView.delegate = self
        recorderPanel.delegate = self
    }

    private func setupLayout() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(mainStackView)

        mainStackView.addArrangedSubview(headerView)
        mainStackView.addArrangedSubview(segmentedControl)
        mainStackView.addArrangedSubview(controlPanel)
        mainStackView.addArrangedSubview(playbackView)
        mainStackView.addArrangedSubview(recorderPanel)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        segmentedControl.snp.makeConstraints { make in
            make.height.equalTo(40)
        }
    }

    private func setupActions() {
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
    }

    private func updateUI() {
        updateVisibleSections()
    }

    // MARK: - Helper Methods
    private func updateVisibleSections() {
        let selectedIndex = segmentedControl.selectedSegmentIndex

        UIView.animate(withDuration: 0.3) {
            self.controlPanel.isHidden = selectedIndex != 0
            self.playbackView.isHidden = selectedIndex != 1
            self.recorderPanel.isHidden = selectedIndex != 2

            self.controlPanel.alpha = selectedIndex == 0 ? 1.0 : 0.0
            self.playbackView.alpha = selectedIndex == 1 ? 1.0 : 0.0
            self.recorderPanel.alpha = selectedIndex == 2 ? 1.0 : 0.0
        }

        // Update subtitle based on current section
        switch selectedIndex {
        case 0:
            subtitleLabel.text = "🌐 Configure Simulation Parameters"
        case 1:
            subtitleLabel.text = "🔊 Experience Noise Simulation"
        case 2:
            subtitleLabel.text = "📋 Record Your Perception"
        default:
            break
        }
    }

    private func showAlert(title: String, message: String, style: UIAlertController.Style = .alert) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: style)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showConfirmationAlert(title: String, message: String, confirmAction: @escaping () -> Void) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Confirm", style: .default) { _ in
            confirmAction()
        })
        present(alert, animated: true)
    }

    // MARK: - Actions
    @objc private func segmentChanged() {
        updateVisibleSections()

        // Auto-scroll to show the active section
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let selectedIndex = self.segmentedControl.selectedSegmentIndex
            var targetView: UIView

            switch selectedIndex {
            case 0:
                targetView = self.controlPanel
            case 1:
                targetView = self.playbackView
            case 2:
                targetView = self.recorderPanel
            default:
                return
            }

            let targetFrame = targetView.convert(targetView.bounds, to: self.scrollView)
            self.scrollView.scrollRectToVisible(targetFrame, animated: true)
        }
    }
}

// MARK: - SimulationControlPanelDelegate
extension PerceptionTestLabViewController: SimulationControlPanelDelegate {

    func didUpdateSettings(_ settings: SimpleSettings) {
        print("Settings updated: \(settings)")
        // Convert SimpleSettings to SimulationSettings if needed
        // For now, just print the settings
    }

    func didSelectScenario(_ index: Int) {
        print("Scenario selected at index: \(index)")

        let scenarios = [
            "Rainy Night Return",
            "Highway Wind Shear",
            "Heavy Traffic Bridge",
            "Parking Garage Rumble",
            "Morning Idle"
        ]

        if index < scenarios.count {
            showAlert(
                title: "Scenario Selected",
                message: "Selected: \(scenarios[index])"
            )
        }
    }

    func didRequestStartSimulation() {
        print("Start simulation requested")

        // Switch to simulation tab
        segmentedControl.selectedSegmentIndex = 1
        updateVisibleSections()

        showAlert(title: "Simulation Started", message: "Switching to simulation view...")
    }
}

// MARK: - NoiseSimulationPlaybackDelegate
extension PerceptionTestLabViewController: NoiseSimulationPlaybackDelegate {

    func simulationDidStart() {
        isSimulationRunning = true
        showAlert(title: "Simulation Started", message: "Experience the noise simulation and prepare to record your perception.")
    }

    func simulationDidComplete() {
        isSimulationRunning = false

        showConfirmationAlert(
            title: "Simulation Complete",
            message: "The simulation has finished. Would you like to record your perception feedback now?"
        ) {
            // Switch to recorder tab
            self.segmentedControl.selectedSegmentIndex = 2
            self.updateVisibleSections()
        }
    }

    func simulationDidPause() {
        // Optional: Handle pause state
    }
}

// MARK: - PerceptionRecorderPanelDelegate
extension PerceptionTestLabViewController: PerceptionRecorderPanelDelegate {

    func didSaveFeedback(_ feedback: PerceptionFeedback) {
        // TODO: Save feedback to persistent storage
        print("Feedback saved: \(feedback)")

        showAlert(
            title: "Feedback Saved",
            message: "Your perception feedback has been recorded successfully. Thank you for your input!"
        )

        // Optionally reset to configuration panel for next test
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.segmentedControl.selectedSegmentIndex = 0
            self.updateVisibleSections()
        }
    }

    func didRequestSaveProfile(_ feedback: PerceptionFeedback) {
        showConfirmationAlert(
            title: "Save Personal Profile",
            message: "This will create a personal perception profile based on your feedback. This profile can be used for future comparisons and analysis."
        ) {
            // TODO: Create and save personal profile
            self.createPersonalProfile(from: feedback)
        }
    }

    private func createPersonalProfile(from feedback: PerceptionFeedback) {
        // TODO: Implement profile creation logic
        let profile = UserProfile(
            id: UUID().uuidString,
            name: "User Profile \(Date())",
            feedbackHistory: [feedback],
            personalCurve: nil,
            createdAt: Date(),
            lastUpdated: Date()
        )

        print("Personal profile created: \(profile)")

        showAlert(
            title: "Profile Created",
            message: "Your personal perception profile has been created and saved. You can use this for future noise analysis and comparisons."
        )
    }
}