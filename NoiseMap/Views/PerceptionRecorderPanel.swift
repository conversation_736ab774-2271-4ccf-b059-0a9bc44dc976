//
//  PerceptionRecorderPanel.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

protocol PerceptionRecorderPanelDelegate: AnyObject {
    func didSaveFeedback(_ feedback: PerceptionFeedback)
    func didRequestSaveProfile(_ feedback: PerceptionFeedback)
}

class PerceptionRecorderPanel: UIView {

    // MARK: - Properties
    weak var delegate: PerceptionRecorderPanelDelegate?

    private var currentScenarioId: String?
    private var currentSettings: SimulationSettings?
    private var selectedTriggerPoints: Set<TriggerPoint> = []

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let mainStackView = UIStackView()

    // Rating Section
    private let ratingCard = CardView(title: "📋 Subjective Perception Rating")
    private let quietnessRatingView = RatingView(title: "🔇 Quietness Level", maxRating: 10)
    private let discomfortSegmentedControl = UISegmentedControl(items: DiscomfortLevel.allCases.map { $0.rawValue })
    private let durationSegmentedControl = UISegmentedControl(items: DrivingDuration.allCases.map { $0.rawValue })

    // Trigger Points Section
    private let triggerCard = CardView(title: "⚠️ Trigger Points")
    private var triggerButtons: [TriggerButton] = []

    // Notes Section
    private let notesCard = CardView(title: "📝 Additional Notes")
    private let notesTextView = UITextView()
    private let voiceNoteButton = ThemedButton(style: .secondary, title: "🎤 Voice Note")

    // Actions Section
    private let actionsCard = CardView(title: "💾 Save Options")
    private let saveFeedbackButton = ThemedButton(style: .accent, title: "Save Feedback")
    private let saveProfileButton = ThemedButton(style: .primary, title: "Save as Personal Profile")

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = AppTheme.Colors.background
        setupScrollView()
        setupRatingSection()
        setupTriggerSection()
        setupNotesSection()
        setupActionsSection()
        setupLayout()
        setupActions()
        updateUI()
    }

    private func setupScrollView() {
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        mainStackView.axis = .vertical
        mainStackView.spacing = AppTheme.Spacing.lg
        mainStackView.alignment = .fill
    }

    private func setupRatingSection() {
        let ratingContainer = UIView()
        let ratingStackView = UIStackView()

        ratingStackView.axis = .vertical
        ratingStackView.spacing = AppTheme.Spacing.lg
        ratingStackView.alignment = .fill

        // Discomfort level container
        let discomfortContainer = createSegmentedControlContainer(
            title: "😰 Discomfort Level",
            control: discomfortSegmentedControl
        )

        // Duration container
        let durationContainer = createSegmentedControlContainer(
            title: "⏱ Recommended Driving Duration",
            control: durationSegmentedControl
        )

        ratingStackView.addArrangedSubview(quietnessRatingView)
        ratingStackView.addArrangedSubview(createSeparator())
        ratingStackView.addArrangedSubview(discomfortContainer)
        ratingStackView.addArrangedSubview(durationContainer)

        ratingContainer.addSubview(ratingStackView)
        ratingStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        ratingCard.addContent(ratingContainer)
    }

    private func setupTriggerSection() {
        let triggerContainer = UIView()
        let triggerStackView = UIStackView()

        triggerStackView.axis = .vertical
        triggerStackView.spacing = AppTheme.Spacing.md
        triggerStackView.alignment = .fill

        // Create trigger point buttons
        for triggerPoint in TriggerPoint.allCases {
            let button = TriggerButton(triggerPoint: triggerPoint)
            button.onToggle = { [weak self] isSelected in
                if isSelected {
                    self?.selectedTriggerPoints.insert(triggerPoint)
                } else {
                    self?.selectedTriggerPoints.remove(triggerPoint)
                }
            }
            triggerButtons.append(button)
            triggerStackView.addArrangedSubview(button)
        }

        triggerContainer.addSubview(triggerStackView)
        triggerStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        triggerCard.addContent(triggerContainer)
    }

    private func setupNotesSection() {
        let notesContainer = UIView()
        let notesStackView = UIStackView()

        notesStackView.axis = .vertical
        notesStackView.spacing = AppTheme.Spacing.md
        notesStackView.alignment = .fill

        // Text view setup
        notesTextView.font = AppTheme.Typography.body
        notesTextView.textColor = AppTheme.Colors.textPrimary
        notesTextView.backgroundColor = AppTheme.Colors.background
        notesTextView.layer.cornerRadius = AppTheme.CornerRadius.small
        notesTextView.layer.borderWidth = 1
        notesTextView.layer.borderColor = AppTheme.Colors.border.cgColor
        notesTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)

        let placeholderLabel = UILabel()
        placeholderLabel.text = "Describe any specific sensations, like 'sound in left rear door', 'glass resonance like drumming', etc."
        placeholderLabel.font = AppTheme.Typography.body
        placeholderLabel.textColor = AppTheme.Colors.textSecondary
        placeholderLabel.numberOfLines = 0

        notesTextView.addSubview(placeholderLabel)
        placeholderLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(16)
            make.trailing.equalToSuperview().inset(20)
        }

        // Hide placeholder when text is entered
        NotificationCenter.default.addObserver(forName: UITextView.textDidChangeNotification, object: notesTextView, queue: .main) { _ in
            placeholderLabel.isHidden = !self.notesTextView.text.isEmpty
        }

        notesStackView.addArrangedSubview(notesTextView)
        notesStackView.addArrangedSubview(voiceNoteButton)

        notesTextView.snp.makeConstraints { make in
            make.height.equalTo(100)
        }

        voiceNoteButton.snp.makeConstraints { make in
            make.height.equalTo(44)
        }

        notesContainer.addSubview(notesStackView)
        notesStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        notesCard.addContent(notesContainer)
    }

    private func setupActionsSection() {
        let actionsContainer = UIView()
        let actionsStackView = UIStackView()

        actionsStackView.axis = .vertical
        actionsStackView.spacing = AppTheme.Spacing.md
        actionsStackView.alignment = .fill

        actionsStackView.addArrangedSubview(saveFeedbackButton)
        actionsStackView.addArrangedSubview(saveProfileButton)

        saveFeedbackButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        saveProfileButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        actionsContainer.addSubview(actionsStackView)
        actionsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        actionsCard.addContent(actionsContainer)
    }

    private func setupLayout() {
        addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(mainStackView)

        mainStackView.addArrangedSubview(ratingCard)
        mainStackView.addArrangedSubview(triggerCard)
        mainStackView.addArrangedSubview(notesCard)
        mainStackView.addArrangedSubview(actionsCard)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupActions() {
        saveFeedbackButton.addTarget(self, action: #selector(saveFeedbackTapped), for: .touchUpInside)
        saveProfileButton.addTarget(self, action: #selector(saveProfileTapped), for: .touchUpInside)
        voiceNoteButton.addTarget(self, action: #selector(voiceNoteTapped), for: .touchUpInside)

        discomfortSegmentedControl.addTarget(self, action: #selector(discomfortChanged), for: .valueChanged)
        durationSegmentedControl.addTarget(self, action: #selector(durationChanged), for: .valueChanged)
    }

    private func updateUI() {
        discomfortSegmentedControl.selectedSegmentIndex = 0
        durationSegmentedControl.selectedSegmentIndex = 2 // Default to "No time limit"
        quietnessRatingView.rating = 5 // Default rating
    }

    // MARK: - Helper Methods
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = AppTheme.Colors.separator
        separator.snp.makeConstraints { make in
            make.height.equalTo(1)
        }
        return separator
    }

    private func createSegmentedControlContainer(title: String, control: UISegmentedControl) -> UIView {
        let container = UIView()
        let titleLabel = UILabel()

        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.callout
        titleLabel.textColor = AppTheme.Colors.textPrimary

        control.selectedSegmentTintColor = AppTheme.Colors.accent
        control.backgroundColor = AppTheme.Colors.background
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textLight], for: .selected)
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textPrimary], for: .normal)

        container.addSubview(titleLabel)
        container.addSubview(control)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        control.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(32)
        }

        return container
    }

    private func createFeedback() -> PerceptionFeedback {
        let discomfortLevel = DiscomfortLevel.allCases[discomfortSegmentedControl.selectedSegmentIndex]
        let duration = DrivingDuration.allCases[durationSegmentedControl.selectedSegmentIndex]

        return PerceptionFeedback(
            quietnessRating: quietnessRatingView.rating,
            discomfortLevel: discomfortLevel,
            recommendedDuration: duration,
            triggerPoints: selectedTriggerPoints,
            freeformNotes: notesTextView.text ?? "",
            timestamp: Date(),
            scenarioId: currentScenarioId ?? "",
            settings: currentSettings ?? SimulationSettings(
                speed: 0, tireNoise: 0, windNoise: 0, structuralNoise: 0,
                windowState: .closed, microphonePosition: .driver,
                temperature: 20, humidity: 60
            )
        )
    }

    private func resetForm() {
        quietnessRatingView.rating = 5
        discomfortSegmentedControl.selectedSegmentIndex = 0
        durationSegmentedControl.selectedSegmentIndex = 2
        selectedTriggerPoints.removeAll()
        triggerButtons.forEach { $0.isSelected = false }
        notesTextView.text = ""
    }

    // MARK: - Actions
    @objc private func saveFeedbackTapped() {
        let feedback = createFeedback()
        delegate?.didSaveFeedback(feedback)

        // Show success message
        showSuccessMessage("Feedback saved successfully!")
        resetForm()
    }

    @objc private func saveProfileTapped() {
        let feedback = createFeedback()
        delegate?.didRequestSaveProfile(feedback)

        // Show success message
        showSuccessMessage("Personal profile saved successfully!")
        resetForm()
    }

    @objc private func voiceNoteTapped() {
        // TODO: Implement voice recording functionality
        showInfoMessage("Voice note feature coming soon!")
    }

    @objc private func discomfortChanged() {
        // Optional: Update UI based on discomfort level selection
    }

    @objc private func durationChanged() {
        // Optional: Update UI based on duration selection
    }

    private func showSuccessMessage(_ message: String) {
        let alert = UIAlertController(title: "Success", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))

        if let viewController = findViewController() {
            viewController.present(alert, animated: true)
        }
    }

    private func showInfoMessage(_ message: String) {
        let alert = UIAlertController(title: "Info", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))

        if let viewController = findViewController() {
            viewController.present(alert, animated: true)
        }
    }

    // MARK: - Public Methods
    func configureForScenario(_ scenarioId: String, settings: SimulationSettings) {
        currentScenarioId = scenarioId
        currentSettings = settings
        resetForm()
    }
}

// MARK: - Custom Rating View
class RatingView: UIView {

    private let titleLabel = UILabel()
    private let ratingStackView = UIStackView()
    private let valueLabel = UILabel()

    private var starButtons: [UIButton] = []
    private let maxRating: Int

    var rating: Int = 0 {
        didSet {
            updateStars()
            valueLabel.text = "\(rating)/\(maxRating)"
        }
    }

    init(title: String, maxRating: Int) {
        self.maxRating = maxRating
        super.init(frame: .zero)
        setupView(title: title)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView(title: String) {
        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.callout
        titleLabel.textColor = AppTheme.Colors.textPrimary

        valueLabel.font = AppTheme.Typography.headline
        valueLabel.textColor = AppTheme.Colors.accent
        valueLabel.text = "\(rating)/\(maxRating)"

        ratingStackView.axis = .horizontal
        ratingStackView.spacing = AppTheme.Spacing.xs
        ratingStackView.alignment = .center

        // Create star buttons
        for i in 1...maxRating {
            let button = UIButton()
            button.setTitle("☆", for: .normal)
            button.setTitle("★", for: .selected)
            button.setTitleColor(AppTheme.Colors.textSecondary, for: .normal)
            button.setTitleColor(AppTheme.Colors.accent, for: .selected)
            button.titleLabel?.font = UIFont.systemFont(ofSize: 24)
            button.tag = i
            button.addTarget(self, action: #selector(starTapped(_:)), for: .touchUpInside)

            starButtons.append(button)
            ratingStackView.addArrangedSubview(button)
        }

        setupLayout()
        updateStars()
    }

    private func setupLayout() {
        addSubview(titleLabel)
        addSubview(valueLabel)
        addSubview(ratingStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }

        ratingStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(40)
        }
    }

    @objc private func starTapped(_ sender: UIButton) {
        rating = sender.tag
    }

    private func updateStars() {
        for (index, button) in starButtons.enumerated() {
            button.isSelected = index < rating
        }
    }
}

// MARK: - Custom Trigger Button
class TriggerButton: UIButton {

    private let triggerPoint: TriggerPoint
    var onToggle: ((Bool) -> Void)?

    override var isSelected: Bool {
        didSet {
            updateAppearance()
            onToggle?(isSelected)
        }
    }

    init(triggerPoint: TriggerPoint) {
        self.triggerPoint = triggerPoint
        super.init(frame: .zero)
        setupButton()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupButton() {
        setTitle("\(triggerPoint.icon) \(triggerPoint.rawValue)", for: .normal)
        titleLabel?.font = AppTheme.Typography.callout
        layer.cornerRadius = AppTheme.CornerRadius.small
        layer.borderWidth = 1
        contentEdgeInsets = UIEdgeInsets(top: 12, left: 16, bottom: 12, right: 16)

        updateAppearance()
        addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
    }

    private func updateAppearance() {
        if isSelected {
            backgroundColor = AppTheme.Colors.accent.withAlphaComponent(0.1)
            layer.borderColor = AppTheme.Colors.accent.cgColor
            setTitleColor(AppTheme.Colors.accent, for: .normal)
        } else {
            backgroundColor = AppTheme.Colors.cardBackground
            layer.borderColor = AppTheme.Colors.border.cgColor
            setTitleColor(AppTheme.Colors.textPrimary, for: .normal)
        }
    }

    @objc private func buttonTapped() {
        isSelected.toggle()
    }
}

// MARK: - UIView Extension
extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}