//
//  SimulationControlPanel.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

// Simple data structures for this view
struct SimpleSettings {
    var speed: Float = 60
    var tireNoise: Float = 50
    var windNoise: Float = 50
    var structuralNoise: Float = 50
    var windowState: Int = 0
    var microphonePosition: Int = 0
    var temperature: Float = 20
    var humidity: Float = 60
}

protocol SimulationControlPanelDelegate: AnyObject {
    func didUpdateSettings(_ settings: SimpleSettings)
    func didSelectScenario(_ index: Int)
    func didRequestStartSimulation()
}

class SimulationControlPanel: UIView {

    // MARK: - Properties
    weak var delegate: SimulationControlPanelDelegate?
    private var currentSettings = SimpleSettings()

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let mainStackView = UIStackView()

    // Scenario Selection
    private let scenarioCard = UIView()
    private let scenarioStackView = UIStackView()
    private var scenarioButtons: [UIButton] = []

    // Custom Configuration
    private let configCard = UIView()
    private let speedSlider = UISlider()
    private let tireNoiseSlider = UISlider()
    private let windNoiseSlider = UISlider()
    private let structuralSlider = UISlider()

    // Environment Settings
    private let environmentCard = UIView()
    private let windowSegmentedControl = UISegmentedControl(items: ["Closed", "Cracked", "Open"])
    private let microphoneSegmentedControl = UISegmentedControl(items: ["Driver", "Passenger", "Rear Left", "Rear Right"])
    private let temperatureSlider = UISlider()
    private let humiditySlider = UISlider()

    // Start Button
    private let startButton = UIButton(type: .system)

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = UIColor.systemBackground
        setupScrollView()
        setupScenarioSection()
        setupConfigurationSection()
        setupEnvironmentSection()
        setupStartButton()
        setupLayout()
        setupActions()
    }

    private func setupScrollView() {
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        mainStackView.axis = .vertical
        mainStackView.spacing = 20
        mainStackView.alignment = .fill
    }

    private func setupScenarioSection() {
        // Setup scenario card
        scenarioCard.backgroundColor = UIColor.systemBackground
        scenarioCard.layer.cornerRadius = 12
        scenarioCard.layer.shadowColor = UIColor.black.cgColor
        scenarioCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        scenarioCard.layer.shadowRadius = 4
        scenarioCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "🌐 Scenario Presets"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        scenarioStackView.axis = .vertical
        scenarioStackView.spacing = 12
        scenarioStackView.alignment = .fill

        // Create scenario buttons
        let scenarios = [
            ("⛆", "Rainy Night Return", "Heavy rain on highway"),
            ("🌬", "Highway Wind Shear", "High-speed crosswinds"),
            ("🛻", "Heavy Traffic Bridge", "Truck rumble overhead"),
            ("🅿️", "Parking Garage Rumble", "Low-frequency resonance"),
            ("🕓", "Morning Idle", "Quiet garage startup")
        ]

        for (index, (icon, title, subtitle)) in scenarios.enumerated() {
            let button = createScenarioButton(icon: icon, title: title, subtitle: subtitle)
            button.tag = index
            button.addTarget(self, action: #selector(scenarioButtonTapped(_:)), for: .touchUpInside)
            scenarioButtons.append(button)
            scenarioStackView.addArrangedSubview(button)
        }

        scenarioCard.addSubview(titleLabel)
        scenarioCard.addSubview(scenarioStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        scenarioStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }

    private func setupConfigurationSection() {
        // Setup config card
        configCard.backgroundColor = UIColor.systemBackground
        configCard.layer.cornerRadius = 12
        configCard.layer.shadowColor = UIColor.black.cgColor
        configCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        configCard.layer.shadowRadius = 4
        configCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "⚙️ Custom Configuration"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        let configStackView = UIStackView()
        configStackView.axis = .vertical
        configStackView.spacing = 20
        configStackView.alignment = .fill

        // Setup sliders
        let speedContainer = createSliderContainer(title: "🌀 Driving Speed", slider: speedSlider, minValue: 0, maxValue: 150, unit: "km/h")
        let tireContainer = createSliderContainer(title: "🛞 Tire Noise", slider: tireNoiseSlider, minValue: 0, maxValue: 100, unit: "%")
        let windContainer = createSliderContainer(title: "💨 Wind Noise", slider: windNoiseSlider, minValue: 0, maxValue: 100, unit: "%")
        let structuralContainer = createSliderContainer(title: "🏗 Structural Transmission", slider: structuralSlider, minValue: 0, maxValue: 100, unit: "%")

        configStackView.addArrangedSubview(speedContainer)
        configStackView.addArrangedSubview(tireContainer)
        configStackView.addArrangedSubview(windContainer)
        configStackView.addArrangedSubview(structuralContainer)

        configCard.addSubview(titleLabel)
        configCard.addSubview(configStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        configStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }

    // MARK: - Helper Methods
    private func createScenarioButton(icon: String, title: String, subtitle: String) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.systemGray6
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 4
        stackView.alignment = .center
        stackView.isUserInteractionEnabled = false

        let iconLabel = UILabel()
        iconLabel.text = icon
        iconLabel.font = UIFont.systemFont(ofSize: 24)

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor.secondaryLabel

        stackView.addArrangedSubview(iconLabel)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)

        button.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.edges.equalToSuperview().inset(12)
        }

        button.snp.makeConstraints { make in
            make.height.equalTo(80)
        }

        return button
    }

    private func createSliderContainer(title: String, slider: UISlider, minValue: Float, maxValue: Float, unit: String) -> UIView {
        let container = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let valueLabel = UILabel()
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        valueLabel.textColor = UIColor.systemBlue

        slider.minimumValue = minValue
        slider.maximumValue = maxValue
        slider.value = minValue + (maxValue - minValue) * 0.5 // Default to middle
        slider.tintColor = UIColor.systemBlue

        // Store valueLabel and unit as associated objects
        objc_setAssociatedObject(slider, "valueLabel", valueLabel, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        objc_setAssociatedObject(slider, "unit", unit, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        // Update initial value
        valueLabel.text = "\(Int(slider.value))\(unit)"

        container.addSubview(titleLabel)
        container.addSubview(valueLabel)
        container.addSubview(slider)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(30)
        }

        return container
    }

    private func createSegmentedControlContainer(title: String, control: UISegmentedControl) -> UIView {
        let container = UIView()
        let titleLabel = UILabel()

        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        control.selectedSegmentTintColor = UIColor.systemBlue
        control.backgroundColor = UIColor.systemGray6
        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        control.setTitleTextAttributes([.foregroundColor: UIColor.label], for: .normal)

        container.addSubview(titleLabel)
        container.addSubview(control)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        control.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(32)
        }

        return container
    }

    // MARK: - Actions
    @objc private func sliderValueChanged(_ sender: UISlider) {
        if let valueLabel = objc_getAssociatedObject(sender, "valueLabel") as? UILabel,
           let unit = objc_getAssociatedObject(sender, "unit") as? String {
            valueLabel.text = "\(Int(sender.value))\(unit)"
        }

        // Update current settings
        switch sender {
        case speedSlider:
            currentSettings.speed = sender.value
        case tireNoiseSlider:
            currentSettings.tireNoise = sender.value
        case windNoiseSlider:
            currentSettings.windNoise = sender.value
        case structuralSlider:
            currentSettings.structuralNoise = sender.value
        case temperatureSlider:
            currentSettings.temperature = sender.value
        case humiditySlider:
            currentSettings.humidity = sender.value
        default:
            break
        }

        delegate?.didUpdateSettings(currentSettings)
    }

    @objc private func segmentedControlChanged(_ sender: UISegmentedControl) {
        switch sender {
        case windowSegmentedControl:
            currentSettings.windowState = sender.selectedSegmentIndex
        case microphoneSegmentedControl:
            currentSettings.microphonePosition = sender.selectedSegmentIndex
        default:
            break
        }

        delegate?.didUpdateSettings(currentSettings)
    }

    @objc private func scenarioButtonTapped(_ sender: UIButton) {
        // Reset all buttons
        scenarioButtons.forEach { button in
            button.backgroundColor = UIColor.systemGray6
            button.layer.borderColor = UIColor.systemGray4.cgColor
        }

        // Highlight selected button
        sender.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        sender.layer.borderColor = UIColor.systemBlue.cgColor

        delegate?.didSelectScenario(sender.tag)
    }

    @objc private func startButtonTapped() {
        delegate?.didRequestStartSimulation()
    }

    private func setupEnvironmentSection() {
        // Setup environment card
        environmentCard.backgroundColor = UIColor.systemBackground
        environmentCard.layer.cornerRadius = 12
        environmentCard.layer.shadowColor = UIColor.black.cgColor
        environmentCard.layer.shadowOffset = CGSize(width: 0, height: 2)
        environmentCard.layer.shadowRadius = 4
        environmentCard.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = "🌡 Environment Settings"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        let environmentStackView = UIStackView()
        environmentStackView.axis = .vertical
        environmentStackView.spacing = 20
        environmentStackView.alignment = .fill

        // Window State
        let windowContainer = createSegmentedControlContainer(
            title: "🪟 Window State",
            control: windowSegmentedControl
        )

        // Microphone Position
        let micContainer = createSegmentedControlContainer(
            title: "🪑 Microphone Position",
            control: microphoneSegmentedControl
        )

        // Temperature and Humidity
        let tempContainer = createSliderContainer(title: "🌡 Temperature", slider: temperatureSlider, minValue: -10, maxValue: 40, unit: "°C")
        let humidityContainer = createSliderContainer(title: "💧 Humidity", slider: humiditySlider, minValue: 0, maxValue: 100, unit: "%")

        environmentStackView.addArrangedSubview(windowContainer)
        environmentStackView.addArrangedSubview(micContainer)
        environmentStackView.addArrangedSubview(tempContainer)
        environmentStackView.addArrangedSubview(humidityContainer)

        environmentCard.addSubview(titleLabel)
        environmentCard.addSubview(environmentStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        environmentStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }

    private func setupStartButton() {
        startButton.setTitle("▶️ Start Simulation", for: .normal)
        startButton.backgroundColor = UIColor.systemBlue
        startButton.setTitleColor(.white, for: .normal)
        startButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        startButton.layer.cornerRadius = 12

        startButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
    }

    private func setupLayout() {
        addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(mainStackView)

        mainStackView.addArrangedSubview(scenarioCard)
        mainStackView.addArrangedSubview(configCard)
        mainStackView.addArrangedSubview(environmentCard)
        mainStackView.addArrangedSubview(startButton)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
            make.height.greaterThanOrEqualToSuperview().priority(250)
        }

        mainStackView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(16)
        }

        // 确保每个卡片都有明确的高度
        scenarioCard.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(300)
        }

        configCard.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(250)
        }

        environmentCard.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(200)
        }
    }

    private func setupActions() {
        speedSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        tireNoiseSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        windNoiseSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        structuralSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        temperatureSlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        humiditySlider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)

        windowSegmentedControl.addTarget(self, action: #selector(segmentedControlChanged(_:)), for: .valueChanged)
        microphoneSegmentedControl.addTarget(self, action: #selector(segmentedControlChanged(_:)), for: .valueChanged)
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)
    }
}
