//
//  SimulationControlPanel.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

protocol SimulationControlPanelDelegate: AnyObject {
    func didUpdateSettings(_ settings: SimulationSettings)
    func didSelectScenario(_ scenario: NoiseScenario)
    func didRequestStartSimulation()
}

class SimulationControlPanel: UIView {

    // MARK: - Properties
    weak var delegate: SimulationControlPanelDelegate?

    private var currentSettings = SimulationSettings(
        speed: 60,
        tireNoise: 0.5,
        windNoise: 0.5,
        structuralNoise: 0.5,
        windowState: .closed,
        microphonePosition: .driver,
        temperature: 20,
        humidity: 60
    )

    private var selectedScenario: NoiseScenario?

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let mainStackView = UIStackView()

    // Scenario Selection
    private let scenarioCard = CardView(title: "🌐 Scenario Presets")
    private let scenarioStackView = UIStackView()
    private var scenarioButtons: [ScenarioButton] = []

    // Custom Configuration
    private let configCard = CardView(title: "⚙️ Custom Configuration")
    private let speedSlider = ThemedSlider(title: "🌀 Driving Speed", minValue: 0, maxValue: 150, unit: "km/h")
    private let tireNoiseSlider = ThemedSlider(title: "🛞 Tire Noise", minValue: 0, maxValue: 1, unit: "")
    private let windNoiseSlider = ThemedSlider(title: "💨 Wind Noise", minValue: 0, maxValue: 1, unit: "")
    private let structuralSlider = ThemedSlider(title: "🏗 Structural Transmission", minValue: 0, maxValue: 1, unit: "")

    // Environment Settings
    private let environmentCard = CardView(title: "🌡 Environment Settings")
    private let windowSegmentedControl = UISegmentedControl(items: WindowState.allCases.map { $0.rawValue })
    private let microphoneSegmentedControl = UISegmentedControl(items: MicrophonePosition.allCases.map { $0.rawValue })
    private let temperatureSlider = ThemedSlider(title: "🌡 Temperature", minValue: -10, maxValue: 40, unit: "°C")
    private let humiditySlider = ThemedSlider(title: "💧 Humidity", minValue: 0, maxValue: 100, unit: "%")

    // Start Button
    private let startButton = ThemedButton(style: .accent, title: "▶️ Start Simulation")

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupView() {
        backgroundColor = AppTheme.Colors.background
        setupScrollView()
        setupScenarioSection()
        setupConfigurationSection()
        setupEnvironmentSection()
        setupStartButton()
        setupLayout()
        setupActions()
        updateUIWithCurrentSettings()
    }

    private func setupScrollView() {
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true

        mainStackView.axis = .vertical
        mainStackView.spacing = AppTheme.Spacing.lg
        mainStackView.alignment = .fill
    }

    private func setupScenarioSection() {
        scenarioStackView.axis = .vertical
        scenarioStackView.spacing = AppTheme.Spacing.md
        scenarioStackView.alignment = .fill

        // Create scenario buttons
        for scenario in NoiseScenario.presets {
            let button = ScenarioButton(
                icon: scenario.icon,
                title: scenario.title,
                subtitle: scenario.subtitle
            )
            button.tag = scenarioButtons.count
            button.addTarget(self, action: #selector(scenarioButtonTapped(_:)), for: .touchUpInside)
            scenarioButtons.append(button)
            scenarioStackView.addArrangedSubview(button)
        }

        scenarioCard.addContent(scenarioStackView)
    }

    private func setupConfigurationSection() {
        let configStackView = UIStackView()
        configStackView.axis = .vertical
        configStackView.spacing = AppTheme.Spacing.lg
        configStackView.alignment = .fill

        configStackView.addArrangedSubview(speedSlider)
        configStackView.addArrangedSubview(createSeparator())
        configStackView.addArrangedSubview(tireNoiseSlider)
        configStackView.addArrangedSubview(windNoiseSlider)
        configStackView.addArrangedSubview(structuralSlider)

        configCard.addContent(configStackView)
    }

    private func setupEnvironmentSection() {
        let environmentStackView = UIStackView()
        environmentStackView.axis = .vertical
        environmentStackView.spacing = AppTheme.Spacing.lg
        environmentStackView.alignment = .fill

        // Window State
        let windowContainer = createSegmentedControlContainer(
            title: "🪟 Window State",
            control: windowSegmentedControl
        )

        // Microphone Position
        let micContainer = createSegmentedControlContainer(
            title: "🪑 Microphone Position",
            control: microphoneSegmentedControl
        )

        environmentStackView.addArrangedSubview(windowContainer)
        environmentStackView.addArrangedSubview(micContainer)
        environmentStackView.addArrangedSubview(createSeparator())
        environmentStackView.addArrangedSubview(temperatureSlider)
        environmentStackView.addArrangedSubview(humiditySlider)

        environmentCard.addContent(environmentStackView)
    }

    private func setupStartButton() {
        startButton.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
    }

    private func setupLayout() {
        addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(mainStackView)

        mainStackView.addArrangedSubview(scenarioCard)
        mainStackView.addArrangedSubview(configCard)
        mainStackView.addArrangedSubview(environmentCard)
        mainStackView.addArrangedSubview(startButton)

        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    private func setupActions() {
        speedSlider.onValueChanged = { [weak self] value in
            self?.currentSettings.speed = value
            self?.notifySettingsChanged()
        }

        tireNoiseSlider.onValueChanged = { [weak self] value in
            self?.currentSettings.tireNoise = value / 100.0
            self?.notifySettingsChanged()
        }

        windNoiseSlider.onValueChanged = { [weak self] value in
            self?.currentSettings.windNoise = value / 100.0
            self?.notifySettingsChanged()
        }

        structuralSlider.onValueChanged = { [weak self] value in
            self?.currentSettings.structuralNoise = value / 100.0
            self?.notifySettingsChanged()
        }

        temperatureSlider.onValueChanged = { [weak self] value in
            self?.currentSettings.temperature = value
            self?.notifySettingsChanged()
        }

        humiditySlider.onValueChanged = { [weak self] value in
            self?.currentSettings.humidity = value
            self?.notifySettingsChanged()
        }

        windowSegmentedControl.addTarget(self, action: #selector(windowStateChanged), for: .valueChanged)
        microphoneSegmentedControl.addTarget(self, action: #selector(microphonePositionChanged), for: .valueChanged)
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)
    }

    // MARK: - Helper Methods
    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = AppTheme.Colors.separator
        separator.snp.makeConstraints { make in
            make.height.equalTo(1)
        }
        return separator
    }

    private func createSegmentedControlContainer(title: String, control: UISegmentedControl) -> UIView {
        let container = UIView()
        let titleLabel = UILabel()

        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.callout
        titleLabel.textColor = AppTheme.Colors.textPrimary

        control.selectedSegmentTintColor = AppTheme.Colors.accent
        control.backgroundColor = AppTheme.Colors.background
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textLight], for: .selected)
        control.setTitleTextAttributes([.foregroundColor: AppTheme.Colors.textPrimary], for: .normal)

        container.addSubview(titleLabel)
        container.addSubview(control)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
        }

        control.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(32)
        }

        return container
    }

    private func updateUIWithCurrentSettings() {
        speedSlider.value = currentSettings.speed
        tireNoiseSlider.value = currentSettings.tireNoise * 100
        windNoiseSlider.value = currentSettings.windNoise * 100
        structuralSlider.value = currentSettings.structuralNoise * 100
        temperatureSlider.value = currentSettings.temperature
        humiditySlider.value = currentSettings.humidity

        windowSegmentedControl.selectedSegmentIndex = WindowState.allCases.firstIndex(of: currentSettings.windowState) ?? 0
        microphoneSegmentedControl.selectedSegmentIndex = MicrophonePosition.allCases.firstIndex(of: currentSettings.microphonePosition) ?? 0
    }

    private func notifySettingsChanged() {
        delegate?.didUpdateSettings(currentSettings)
    }

    // MARK: - Actions
    @objc private func scenarioButtonTapped(_ sender: ScenarioButton) {
        // Deselect all other buttons
        scenarioButtons.forEach { $0.isSelectedState = false }

        // Select tapped button
        sender.isSelectedState = true

        // Update settings with scenario preset
        let scenario = NoiseScenario.presets[sender.tag]
        selectedScenario = scenario
        currentSettings = scenario.defaultSettings
        updateUIWithCurrentSettings()

        delegate?.didSelectScenario(scenario)
        notifySettingsChanged()
    }

    @objc private func windowStateChanged() {
        let selectedIndex = windowSegmentedControl.selectedSegmentIndex
        currentSettings.windowState = WindowState.allCases[selectedIndex]
        notifySettingsChanged()
    }

    @objc private func microphonePositionChanged() {
        let selectedIndex = microphoneSegmentedControl.selectedSegmentIndex
        currentSettings.microphonePosition = MicrophonePosition.allCases[selectedIndex]
        notifySettingsChanged()
    }

    @objc private func startButtonTapped() {
        delegate?.didRequestStartSimulation()
    }

    // MARK: - Public Methods
    func updateSettings(_ settings: SimulationSettings) {
        currentSettings = settings
        updateUIWithCurrentSettings()
    }

    func getCurrentSettings() -> SimulationSettings {
        return currentSettings
    }
}