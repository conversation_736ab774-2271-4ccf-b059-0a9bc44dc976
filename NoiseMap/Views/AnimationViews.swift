//
//  AnimationViews.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

// MARK: - Car Interior View
class CarInteriorView: UIView {

    private var vibrationLayers: [CAShapeLayer] = []
    private var resonancePoints: [ResonancePoint] = []
    private var animationTimer: Timer?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView() {
        backgroundColor = AppTheme.Colors.darkBackground
        setupCarStructure()
        setupResonancePoints()
    }

    private func setupCarStructure() {
        // Draw basic car interior outline
        let carPath = UIBezierPath()

        // Car roof
        carPath.move(to: CGPoint(x: 20, y: 40))
        carPath.addLine(to: CGPoint(x: bounds.width - 20, y: 40))

        // Right side
        carPath.addLine(to: CGPoint(x: bounds.width - 20, y: bounds.height - 40))

        // Floor
        carPath.addLine(to: CGPoint(x: 20, y: bounds.height - 40))

        // Left side
        carPath.addLine(to: CGPoint(x: 20, y: 40))

        let carLayer = CAShapeLayer()
        carLayer.path = carPath.cgPath
        carLayer.strokeColor = AppTheme.Colors.textLight.cgColor
        carLayer.fillColor = UIColor.clear.cgColor
        carLayer.lineWidth = 2

        layer.addSublayer(carLayer)

        // Add seats
        addSeat(at: CGPoint(x: 60, y: 80), isDriver: true)
        addSeat(at: CGPoint(x: bounds.width - 60, y: 80), isDriver: false)
        addSeat(at: CGPoint(x: 60, y: 140), isDriver: false)
        addSeat(at: CGPoint(x: bounds.width - 60, y: 140), isDriver: false)
    }

    private func addSeat(at position: CGPoint, isDriver: Bool) {
        let seatView = UIView()
        seatView.backgroundColor = isDriver ? AppTheme.Colors.accent.withAlphaComponent(0.3) : AppTheme.Colors.textSecondary.withAlphaComponent(0.3)
        seatView.layer.cornerRadius = 8

        addSubview(seatView)
        seatView.frame = CGRect(x: position.x - 15, y: position.y - 10, width: 30, height: 20)

        if isDriver {
            let micIcon = UILabel()
            micIcon.text = "🎤"
            micIcon.font = UIFont.systemFont(ofSize: 12)
            micIcon.textAlignment = .center
            seatView.addSubview(micIcon)
            micIcon.frame = seatView.bounds
        }
    }

    private func setupResonancePoints() {
        // Define key resonance points in the car
        resonancePoints = [
            ResonancePoint(position: CGPoint(x: 50, y: 60), type: .door, frequency: .low),
            ResonancePoint(position: CGPoint(x: bounds.width - 50, y: 60), type: .door, frequency: .medium),
            ResonancePoint(position: CGPoint(x: bounds.width / 2, y: 50), type: .roof, frequency: .high),
            ResonancePoint(position: CGPoint(x: 30, y: bounds.height - 50), type: .floor, frequency: .low),
            ResonancePoint(position: CGPoint(x: bounds.width - 30, y: bounds.height - 50), type: .floor, frequency: .low)
        ]

        for point in resonancePoints {
            let pointLayer = CAShapeLayer()
            pointLayer.path = UIBezierPath(ovalIn: CGRect(x: -3, y: -3, width: 6, height: 6)).cgPath
            pointLayer.position = point.position
            pointLayer.fillColor = point.frequency.color.cgColor
            pointLayer.opacity = 0.7
            layer.addSublayer(pointLayer)
        }
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        // Update paths when view size changes
        layer.sublayers?.removeAll()
        setupCarStructure()
        setupResonancePoints()
    }

    func startVisualization(with settings: SimulationSettings) {
        stopVisualization()

        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateVibrations(with: settings)
        }
    }

    func stopVisualization() {
        animationTimer?.invalidate()
        animationTimer = nil

        vibrationLayers.forEach { $0.removeFromSuperlayer() }
        vibrationLayers.removeAll()
    }

    private func updateVibrations(with settings: SimulationSettings) {
        // Create vibration lines based on noise levels
        createVibrationLines(intensity: settings.structuralNoise)
        animateResonancePoints(with: settings)
    }

    private func createVibrationLines(intensity: Float) {
        // Remove old vibration lines
        vibrationLayers.forEach { $0.removeFromSuperlayer() }
        vibrationLayers.removeAll()

        let lineCount = Int(intensity * 10) + 2

        for _ in 0..<lineCount {
            let startX = CGFloat.random(in: 20...(bounds.width - 20))
            let startY = CGFloat.random(in: 40...(bounds.height - 40))
            let endX = startX + CGFloat.random(in: -20...20)
            let endY = startY + CGFloat.random(in: -20...20)

            let path = UIBezierPath()
            path.move(to: CGPoint(x: startX, y: startY))
            path.addLine(to: CGPoint(x: endX, y: endY))

            let vibrationLayer = CAShapeLayer()
            vibrationLayer.path = path.cgPath
            vibrationLayer.strokeColor = AppTheme.Colors.warning.withAlphaComponent(0.6).cgColor
            vibrationLayer.lineWidth = 1
            vibrationLayer.opacity = Float(intensity)

            layer.addSublayer(vibrationLayer)
            vibrationLayers.append(vibrationLayer)

            // Animate the vibration line
            let animation = CABasicAnimation(keyPath: "opacity")
            animation.fromValue = Float(intensity)
            animation.toValue = 0
            animation.duration = 0.5
            animation.autoreverses = true
            animation.repeatCount = .infinity
            vibrationLayer.add(animation, forKey: "vibration")
        }
    }

    private func animateResonancePoints(with settings: SimulationSettings) {
        for (index, point) in resonancePoints.enumerated() {
            let intensity = getIntensityForPoint(point, settings: settings)

            if let pointLayer = layer.sublayers?[safe: index + 1] as? CAShapeLayer {
                let scaleAnimation = CABasicAnimation(keyPath: "transform.scale")
                scaleAnimation.fromValue = 1.0
                scaleAnimation.toValue = 1.0 + intensity * 0.5
                scaleAnimation.duration = 0.3
                scaleAnimation.autoreverses = true
                scaleAnimation.repeatCount = .infinity
                pointLayer.add(scaleAnimation, forKey: "pulse")
            }
        }
    }

    private func getIntensityForPoint(_ point: ResonancePoint, settings: SimulationSettings) -> Double {
        switch point.type {
        case .door:
            return Double(settings.windNoise * 0.8 + settings.structuralNoise * 0.2)
        case .roof:
            return Double(settings.windNoise * 0.9 + settings.tireNoise * 0.1)
        case .floor:
            return Double(settings.tireNoise * 0.7 + settings.structuralNoise * 0.3)
        }
    }
}

// MARK: - Sound Wave View
class SoundWaveView: UIView {

    private var waveLayer: CAShapeLayer?
    private var displayLink: CADisplayLink?
    private var phase: CGFloat = 0
    private var amplitude: CGFloat = 20
    private var frequency: CGFloat = 0.02

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView() {
        backgroundColor = UIColor.clear

        waveLayer = CAShapeLayer()
        waveLayer?.strokeColor = AppTheme.Colors.accentLight.withAlphaComponent(0.6).cgColor
        waveLayer?.fillColor = UIColor.clear.cgColor
        waveLayer?.lineWidth = 2
        layer.addSublayer(waveLayer!)
    }

    func startWaveAnimation(with settings: SimulationSettings) {
        stopWaveAnimation()

        // Adjust wave parameters based on settings
        amplitude = CGFloat(20 + settings.windNoise * 30 + settings.tireNoise * 20)
        frequency = CGFloat(0.01 + Double(settings.speed) * 0.0001)

        displayLink = CADisplayLink(target: self, selector: #selector(updateWave))
        displayLink?.add(to: .main, forMode: .common)
    }

    func stopWaveAnimation() {
        displayLink?.invalidate()
        displayLink = nil
        waveLayer?.path = nil
    }

    @objc private func updateWave() {
        phase += frequency

        let path = UIBezierPath()
        let width = bounds.width
        let height = bounds.height
        let midY = height / 2

        path.move(to: CGPoint(x: 0, y: midY))

        for x in stride(from: 0, through: width, by: 2) {
            let y = midY + amplitude * sin(phase + x * 0.02)
            path.addLine(to: CGPoint(x: x, y: y))
        }

        waveLayer?.path = path.cgPath
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        waveLayer?.frame = bounds
    }
}

// MARK: - Supporting Types
struct ResonancePoint {
    let position: CGPoint
    let type: ResonanceType
    let frequency: FrequencyRange
}

enum ResonanceType {
    case door, roof, floor
}

enum FrequencyRange {
    case low, medium, high

    var color: UIColor {
        switch self {
        case .low: return AppTheme.Colors.lowNoise
        case .medium: return AppTheme.Colors.mediumNoise
        case .high: return AppTheme.Colors.highNoise
        }
    }
}

// MARK: - Array Extension
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}