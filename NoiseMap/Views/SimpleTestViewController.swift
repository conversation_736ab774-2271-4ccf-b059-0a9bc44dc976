//
//  SimpleTestViewController.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class SimpleTestViewController: UIViewController {

    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let stackView = UIStackView()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
    }

    private func setupView() {
        view.backgroundColor = UIColor.systemBackground
        title = "噪音感知测试室"

        // 设置滚动视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(stackView)

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        // 配置堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.alignment = .fill

        // 添加标题
        let titleLabel = UILabel()
        titleLabel.text = "🌐 模拟配置面板"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textAlignment = .center
        stackView.addArrangedSubview(titleLabel)

        // 添加情景选择
        addScenarioSection()

        // 添加自定义配置
        addCustomConfigSection()

        // 添加开始按钮
        addStartButton()
    }

    private func addScenarioSection() {
        let sectionView = createSectionView(title: "情景选择")

        let scenarios = [
            ("⛆", "暴雨夜返程", "高速公路暴雨驾驶"),
            ("🌬", "高速匝道风切", "高速行驶强侧风"),
            ("🛻", "高架桥重车碾压", "重型卡车通过"),
            ("🅿️", "停车场低频轰鸣", "地下停车场共振"),
            ("🕓", "怠速早晨车库", "住宅车库启动")
        ]

        for (icon, title, subtitle) in scenarios {
            let button = createScenarioButton(icon: icon, title: title, subtitle: subtitle)
            sectionView.addArrangedSubview(button)
        }

        stackView.addArrangedSubview(sectionView)
    }

    private func addCustomConfigSection() {
        let sectionView = createSectionView(title: "自定义配置")

        // 行驶速度
        let speedSlider = createSlider(title: "🌀 行驶速度", value: 60, maxValue: 150, unit: "km/h")
        sectionView.addArrangedSubview(speedSlider)

        // 胎噪占比
        let tireSlider = createSlider(title: "🛞 胎噪占比", value: 50, maxValue: 100, unit: "%")
        sectionView.addArrangedSubview(tireSlider)

        // 风噪占比
        let windSlider = createSlider(title: "💨 风噪占比", value: 50, maxValue: 100, unit: "%")
        sectionView.addArrangedSubview(windSlider)

        // 结构传导占比
        let structuralSlider = createSlider(title: "🏗 结构传导占比", value: 50, maxValue: 100, unit: "%")
        sectionView.addArrangedSubview(structuralSlider)

        stackView.addArrangedSubview(sectionView)
    }

    private func addStartButton() {
        let button = UIButton(type: .system)
        button.setTitle("▶️ 开始模拟", for: .normal)
        button.backgroundColor = UIColor.systemBlue
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(startSimulation), for: .touchUpInside)

        button.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        stackView.addArrangedSubview(button)
    }

    private func createSectionView(title: String) -> UIStackView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor.systemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        containerView.layer.shadowOpacity = 0.1

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.label

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill

        containerView.addSubview(titleLabel)
        containerView.addSubview(stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }

        // 将容器包装在另一个stackView中以便添加到主stackView
        let wrapperStackView = UIStackView()
        wrapperStackView.axis = .vertical
        wrapperStackView.addArrangedSubview(containerView)

        return stackView
    }

    private func createScenarioButton(icon: String, title: String, subtitle: String) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = UIColor.systemGray6
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 4
        stackView.alignment = .center
        stackView.isUserInteractionEnabled = false

        let iconLabel = UILabel()
        iconLabel.text = icon
        iconLabel.font = UIFont.systemFont(ofSize: 24)

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor.secondaryLabel

        stackView.addArrangedSubview(iconLabel)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)

        button.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.edges.equalToSuperview().inset(12)
        }

        button.snp.makeConstraints { make in
            make.height.equalTo(80)
        }

        button.addTarget(self, action: #selector(scenarioButtonTapped(_:)), for: .touchUpInside)

        return button
    }

    private func createSlider(title: String, value: Float, maxValue: Float, unit: String) -> UIView {
        let containerView = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let valueLabel = UILabel()
        valueLabel.text = "\(Int(value))\(unit)"
        valueLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        valueLabel.textColor = UIColor.systemBlue

        let slider = UISlider()
        slider.minimumValue = 0
        slider.maximumValue = maxValue
        slider.value = value
        slider.tintColor = UIColor.systemBlue
        slider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)

        // 将valueLabel存储为slider的关联对象，以便在滑动时更新
        objc_setAssociatedObject(slider, "valueLabel", valueLabel, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        objc_setAssociatedObject(slider, "unit", unit, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        containerView.addSubview(slider)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(30)
        }

        return containerView
    }

    @objc private func scenarioButtonTapped(_ sender: UIButton) {
        // 重置所有按钮状态
        for case let button as UIButton in stackView.arrangedSubviews.flatMap({ view in
            if let stackView = view as? UIStackView {
                return stackView.arrangedSubviews
            }
            return []
        }) {
            button.backgroundColor = UIColor.systemGray6
            button.layer.borderColor = UIColor.systemGray4.cgColor
        }

        // 设置选中状态
        sender.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        sender.layer.borderColor = UIColor.systemBlue.cgColor

        let alert = UIAlertController(title: "情景已选择", message: "已选择该噪音情景", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func sliderValueChanged(_ sender: UISlider) {
        if let valueLabel = objc_getAssociatedObject(sender, "valueLabel") as? UILabel,
           let unit = objc_getAssociatedObject(sender, "unit") as? String {
            valueLabel.text = "\(Int(sender.value))\(unit)"
        }
    }

    @objc private func startSimulation() {
        let alert = UIAlertController(title: "开始模拟", message: "模拟功能正在开发中...", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}