//
//  NoiseModels.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import Foundation

// MARK: - Noise Scenario
struct NoiseScenario {
    let id: String
    let icon: String
    let title: String
    let subtitle: String
    let description: String
    let defaultSettings: SimulationSettings

    static let presets: [NoiseScenario] = [
        NoiseScenario(
            id: "rainy_night",
            icon: "⛆",
            title: "Rainy Night Return",
            subtitle: "Heavy rain on highway",
            description: "Simulates driving home during heavy rainfall with increased tire noise and wind resistance",
            defaultSettings: SimulationSettings(
                speed: 80,
                tireNoise: 0.7,
                windNoise: 0.8,
                structuralNoise: 0.4,
                windowState: .closed,
                microphonePosition: .driver,
                temperature: 15,
                humidity: 85
            )
        ),
        NoiseScenario(
            id: "highway_wind",
            icon: "🌬",
            title: "Highway Wind Shear",
            subtitle: "High-speed wind cutting",
            description: "High-speed driving with strong crosswinds creating wind shear effects",
            defaultSettings: SimulationSettings(
                speed: 120,
                tireNoise: 0.6,
                windNoise: 0.9,
                structuralNoise: 0.5,
                windowState: .closed,
                microphonePosition: .driver,
                temperature: 25,
                humidity: 45
            )
        ),
        NoiseScenario(
            id: "heavy_traffic",
            icon: "🛻",
            title: "Heavy Traffic Bridge",
            subtitle: "Truck rumble overhead",
            description: "Driving under heavy truck traffic on elevated highways",
            defaultSettings: SimulationSettings(
                speed: 60,
                tireNoise: 0.5,
                windNoise: 0.3,
                structuralNoise: 0.8,
                windowState: .closed,
                microphonePosition: .driver,
                temperature: 20,
                humidity: 60
            )
        ),
        NoiseScenario(
            id: "parking_rumble",
            icon: "🅿️",
            title: "Parking Garage Rumble",
            subtitle: "Low-frequency resonance",
            description: "Underground parking with engine resonance and structural vibrations",
            defaultSettings: SimulationSettings(
                speed: 20,
                tireNoise: 0.3,
                windNoise: 0.1,
                structuralNoise: 0.7,
                windowState: .closed,
                microphonePosition: .driver,
                temperature: 18,
                humidity: 70
            )
        ),
        NoiseScenario(
            id: "idle_morning",
            icon: "🕓",
            title: "Morning Idle",
            subtitle: "Quiet garage startup",
            description: "Early morning engine idle in residential garage",
            defaultSettings: SimulationSettings(
                speed: 0,
                tireNoise: 0.0,
                windNoise: 0.0,
                structuralNoise: 0.3,
                windowState: .closed,
                microphonePosition: .driver,
                temperature: 12,
                humidity: 55
            )
        )
    ]
}

// MARK: - Simulation Settings
struct SimulationSettings {
    var speed: Float // km/h
    var tireNoise: Float // 0.0 - 1.0
    var windNoise: Float // 0.0 - 1.0
    var structuralNoise: Float // 0.0 - 1.0
    var windowState: WindowState
    var microphonePosition: MicrophonePosition
    var temperature: Float // Celsius
    var humidity: Float // Percentage
}

enum WindowState: String, CaseIterable {
    case closed = "Fully Closed"
    case cracked = "Slightly Open"
    case open = "Open"

    var icon: String {
        switch self {
        case .closed: return "🪟"
        case .cracked: return "🪟"
        case .open: return "🪟"
        }
    }
}

enum MicrophonePosition: String, CaseIterable {
    case driver = "Driver Seat"
    case passenger = "Passenger Seat"
    case rearLeft = "Rear Left"
    case rearRight = "Rear Right"

    var icon: String {
        switch self {
        case .driver: return "🪑"
        case .passenger: return "🪑"
        case .rearLeft: return "🪑"
        case .rearRight: return "🪑"
        }
    }
}

// MARK: - Perception Feedback
struct PerceptionFeedback {
    var quietnessRating: Int // 1-10
    var discomfortLevel: DiscomfortLevel
    var recommendedDuration: DrivingDuration
    var triggerPoints: Set<TriggerPoint>
    var freeformNotes: String
    var timestamp: Date
    var scenarioId: String
    var settings: SimulationSettings
}

enum DiscomfortLevel: String, CaseIterable {
    case none = "No Discomfort"
    case earPressure = "Ear Pressure"
    case irritation = "Irritation"
    case attentionLoss = "Attention Loss"

    var severity: Int {
        switch self {
        case .none: return 0
        case .earPressure: return 1
        case .irritation: return 2
        case .attentionLoss: return 3
        }
    }
}

enum DrivingDuration: String, CaseIterable {
    case short = "< 30 minutes"
    case medium = "30-60 minutes"
    case unlimited = "No time limit"
}

enum TriggerPoint: String, CaseIterable {
    case wantToSlowDown = "Want to slow down"
    case wantToOpenWindow = "Want to open window"
    case suspectAbnormalNoise = "Suspect abnormal noise"
    case feelPressure = "Feel pressure/mood affected"

    var icon: String {
        switch self {
        case .wantToSlowDown: return "🐌"
        case .wantToOpenWindow: return "🪟"
        case .suspectAbnormalNoise: return "🔊"
        case .feelPressure: return "😰"
        }
    }
}

// MARK: - User Profile
struct UserProfile {
    let id: String
    var name: String
    var feedbackHistory: [PerceptionFeedback]
    var personalCurve: PersonalPerceptionCurve?
    var createdAt: Date
    var lastUpdated: Date
}

struct PersonalPerceptionCurve {
    let profileId: String
    let averageQuietnessRating: Double
    let commonTriggerPoints: [TriggerPoint]
    let preferredDuration: DrivingDuration
    let sensitivityToFrequencies: [String: Double] // Frequency range to sensitivity mapping
    let generatedAt: Date
}