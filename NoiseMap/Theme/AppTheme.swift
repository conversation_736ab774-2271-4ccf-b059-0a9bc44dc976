//
//  AppTheme.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit

struct AppTheme {

    // MARK: - Colors
    struct Colors {
        // Primary Theme Colors - Professional Audio/Automotive
        static let primary = UIColor(red: 0.12, green: 0.16, blue: 0.24, alpha: 1.0)        // Deep Navy
        static let primaryLight = UIColor(red: 0.18, green: 0.24, blue: 0.36, alpha: 1.0)  // Lighter Navy
        static let accent = UIColor(red: 0.00, green: 0.48, blue: 0.80, alpha: 1.0)        // Professional Blue
        static let accentLight = UIColor(red: 0.20, green: 0.60, blue: 0.90, alpha: 1.0)   // Light Blue

        // Secondary Colors
        static let success = UIColor(red: 0.20, green: 0.78, blue: 0.35, alpha: 1.0)       // Green
        static let warning = UIColor(red: 1.00, green: 0.58, blue: 0.00, alpha: 1.0)       // Orange
        static let danger = UIColor(red: 0.96, green: 0.26, blue: 0.21, alpha: 1.0)        // Red

        // Noise Level Colors
        static let lowNoise = UIColor(red: 0.20, green: 0.78, blue: 0.35, alpha: 1.0)      // Green
        static let mediumNoise = UIColor(red: 1.00, green: 0.58, blue: 0.00, alpha: 1.0)   // Orange
        static let highNoise = UIColor(red: 0.96, green: 0.26, blue: 0.21, alpha: 1.0)     // Red

        // Background Colors
        static let background = UIColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0)    // Light Gray
        static let cardBackground = UIColor.white
        static let darkBackground = UIColor(red: 0.08, green: 0.10, blue: 0.14, alpha: 1.0) // Very Dark Navy

        // Text Colors
        static let textPrimary = UIColor(red: 0.12, green: 0.16, blue: 0.24, alpha: 1.0)
        static let textSecondary = UIColor(red: 0.45, green: 0.50, blue: 0.55, alpha: 1.0)
        static let textLight = UIColor.white

        // Border and Separator
        static let border = UIColor(red: 0.85, green: 0.85, blue: 0.87, alpha: 1.0)
        static let separator = UIColor(red: 0.90, green: 0.90, blue: 0.92, alpha: 1.0)
    }

    // MARK: - Typography
    struct Typography {
        static let largeTitle = UIFont.systemFont(ofSize: 28, weight: .bold)
        static let title1 = UIFont.systemFont(ofSize: 24, weight: .semibold)
        static let title2 = UIFont.systemFont(ofSize: 20, weight: .semibold)
        static let title3 = UIFont.systemFont(ofSize: 18, weight: .medium)
        static let headline = UIFont.systemFont(ofSize: 16, weight: .semibold)
        static let body = UIFont.systemFont(ofSize: 16, weight: .regular)
        static let callout = UIFont.systemFont(ofSize: 14, weight: .medium)
        static let subheadline = UIFont.systemFont(ofSize: 14, weight: .regular)
        static let footnote = UIFont.systemFont(ofSize: 12, weight: .regular)
        static let caption = UIFont.systemFont(ofSize: 11, weight: .regular)
    }

    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
    }

    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }

    // MARK: - Shadow
    struct Shadow {
        static let light = ShadowStyle(
            color: UIColor.black.withAlphaComponent(0.05),
            offset: CGSize(width: 0, height: 2),
            radius: 4,
            opacity: 1.0
        )

        static let medium = ShadowStyle(
            color: UIColor.black.withAlphaComponent(0.1),
            offset: CGSize(width: 0, height: 4),
            radius: 8,
            opacity: 1.0
        )

        static let heavy = ShadowStyle(
            color: UIColor.black.withAlphaComponent(0.15),
            offset: CGSize(width: 0, height: 8),
            radius: 16,
            opacity: 1.0
        )
    }

    struct ShadowStyle {
        let color: UIColor
        let offset: CGSize
        let radius: CGFloat
        let opacity: Float
    }
}

// MARK: - UIView Extensions
extension UIView {
    func applyShadow(_ shadow: AppTheme.ShadowStyle) {
        layer.shadowColor = shadow.color.cgColor
        layer.shadowOffset = shadow.offset
        layer.shadowRadius = shadow.radius
        layer.shadowOpacity = shadow.opacity
        layer.masksToBounds = false
    }

    func applyCardStyle() {
        backgroundColor = AppTheme.Colors.cardBackground
        layer.cornerRadius = AppTheme.CornerRadius.medium
        applyShadow(AppTheme.Shadow.light)
    }
}