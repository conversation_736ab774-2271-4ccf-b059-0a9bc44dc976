//
//  CustomComponents.swift
//  NoiseMap
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

// MARK: - Custom Button
class ThemedButton: UIButton {

    enum ButtonStyle {
        case primary
        case secondary
        case accent
        case danger
    }

    private let style: ButtonStyle

    init(style: ButtonStyle, title: String) {
        self.style = style
        super.init(frame: .zero)
        setupButton(title: title)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupButton(title: String) {
        setTitle(title, for: .normal)
        titleLabel?.font = AppTheme.Typography.callout
        layer.cornerRadius = AppTheme.CornerRadius.small

        switch style {
        case .primary:
            backgroundColor = AppTheme.Colors.primary
            setTitleColor(AppTheme.Colors.textLight, for: .normal)
        case .secondary:
            backgroundColor = AppTheme.Colors.background
            setTitleColor(AppTheme.Colors.textPrimary, for: .normal)
            layer.borderWidth = 1
            layer.borderColor = AppTheme.Colors.border.cgColor
        case .accent:
            backgroundColor = AppTheme.Colors.accent
            setTitleColor(AppTheme.Colors.textLight, for: .normal)
        case .danger:
            backgroundColor = AppTheme.Colors.danger
            setTitleColor(AppTheme.Colors.textLight, for: .normal)
        }

        // Add touch feedback
        addTarget(self, action: #selector(buttonTouchDown), for: .touchDown)
        addTarget(self, action: #selector(buttonTouchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }

    @objc private func buttonTouchDown() {
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            self.alpha = 0.8
        }
    }

    @objc private func buttonTouchUp() {
        UIView.animate(withDuration: 0.1) {
            self.transform = .identity
            self.alpha = 1.0
        }
    }
}

// MARK: - Custom Slider
class ThemedSlider: UIView {

    private let titleLabel = UILabel()
    private let valueLabel = UILabel()
    private let slider = UISlider()
    private let unitLabel = UILabel()

    var value: Float {
        get { return slider.value }
        set {
            slider.value = newValue
            updateValueLabel()
        }
    }

    var onValueChanged: ((Float) -> Void)?

    init(title: String, minValue: Float, maxValue: Float, unit: String = "") {
        super.init(frame: .zero)
        setupSlider(title: title, minValue: minValue, maxValue: maxValue, unit: unit)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupSlider(title: String, minValue: Float, maxValue: Float, unit: String) {
        // Title
        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.callout
        titleLabel.textColor = AppTheme.Colors.textPrimary

        // Value
        valueLabel.font = AppTheme.Typography.headline
        valueLabel.textColor = AppTheme.Colors.accent
        valueLabel.textAlignment = .right

        // Unit
        unitLabel.text = unit
        unitLabel.font = AppTheme.Typography.caption
        unitLabel.textColor = AppTheme.Colors.textSecondary

        // Slider
        slider.minimumValue = minValue
        slider.maximumValue = maxValue
        slider.value = minValue
        slider.tintColor = AppTheme.Colors.accent
        slider.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)

        updateValueLabel()
        setupLayout()
    }

    private func setupLayout() {
        addSubview(titleLabel)
        addSubview(valueLabel)
        addSubview(unitLabel)
        addSubview(slider)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.trailing.equalTo(unitLabel.snp.leading).offset(-AppTheme.Spacing.xs)
        }

        unitLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
            make.width.equalTo(30)
        }

        slider.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(30)
        }
    }

    @objc private func sliderValueChanged() {
        updateValueLabel()
        onValueChanged?(slider.value)
    }

    private func updateValueLabel() {
        valueLabel.text = String(format: "%.0f", slider.value)
    }
}

// MARK: - Custom Card View
class CardView: UIView {

    private let titleLabel = UILabel()
    private let contentStackView = UIStackView()

    init(title: String) {
        super.init(frame: .zero)
        setupCard(title: title)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupCard(title: String) {
        applyCardStyle()

        titleLabel.text = title
        titleLabel.font = AppTheme.Typography.headline
        titleLabel.textColor = AppTheme.Colors.textPrimary

        contentStackView.axis = .vertical
        contentStackView.spacing = AppTheme.Spacing.md
        contentStackView.alignment = .fill

        setupLayout()
    }

    private func setupLayout() {
        addSubview(titleLabel)
        addSubview(contentStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.md)
        }

        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(AppTheme.Spacing.md)
            make.leading.trailing.bottom.equalToSuperview().inset(AppTheme.Spacing.md)
        }
    }

    func addContent(_ view: UIView) {
        contentStackView.addArrangedSubview(view)
    }
}

// MARK: - Scenario Selection Button
class ScenarioButton: UIButton {

    private let iconLabel = UILabel()
    private let titleLabel1 = UILabel()
    private let subtitleLabel1 = UILabel()

    var isSelectedState: Bool = false {
        didSet {
            updateAppearance()
        }
    }

    init(icon: String, title: String, subtitle: String) {
        super.init(frame: .zero)
        setupButton(icon: icon, title: title, subtitle: subtitle)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupButton(icon: String, title: String, subtitle: String) {
        layer.cornerRadius = AppTheme.CornerRadius.medium
        layer.borderWidth = 2
        applyShadow(AppTheme.Shadow.light)

        iconLabel.text = icon
        iconLabel.font = UIFont.systemFont(ofSize: 24)
        iconLabel.textAlignment = .center

        titleLabel1.text = title
        titleLabel1.font = AppTheme.Typography.callout
        titleLabel1.textAlignment = .center
        titleLabel1.numberOfLines = 0

        subtitleLabel1.text = subtitle
        subtitleLabel1.font = AppTheme.Typography.caption
        subtitleLabel1.textAlignment = .center
        subtitleLabel1.numberOfLines = 0

        updateAppearance()
        setupLayout()

        addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
    }

    private func setupLayout() {
        addSubview(iconLabel)
        addSubview(titleLabel1)
        addSubview(subtitleLabel1)

        iconLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(AppTheme.Spacing.md)
            make.centerX.equalToSuperview()
            make.height.equalTo(30)
        }

        titleLabel1.snp.makeConstraints { make in
            make.top.equalTo(iconLabel.snp.bottom).offset(AppTheme.Spacing.sm)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.sm)
        }

        subtitleLabel1.snp.makeConstraints { make in
            make.top.equalTo(titleLabel1.snp.bottom).offset(AppTheme.Spacing.xs)
            make.leading.trailing.equalToSuperview().inset(AppTheme.Spacing.sm)
            make.bottom.equalToSuperview().offset(-AppTheme.Spacing.md)
        }
    }

    private func updateAppearance() {
        if isSelectedState {
            backgroundColor = AppTheme.Colors.accentLight.withAlphaComponent(0.1)
            layer.borderColor = AppTheme.Colors.accent.cgColor
            iconLabel.textColor = AppTheme.Colors.accent
            titleLabel1.textColor = AppTheme.Colors.accent
            subtitleLabel1.textColor = AppTheme.Colors.accent
        } else {
            backgroundColor = AppTheme.Colors.cardBackground
            layer.borderColor = AppTheme.Colors.border.cgColor
            iconLabel.textColor = AppTheme.Colors.textSecondary
            titleLabel1.textColor = AppTheme.Colors.textPrimary
            subtitleLabel1.textColor = AppTheme.Colors.textSecondary
        }
    }

    @objc private func buttonTapped() {
        isSelectedState.toggle()
    }
}
